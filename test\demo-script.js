// 演示腳本 - 用於測試聯絡人提取功能
// 可以在瀏覽器控制台中直接運行此腳本來測試功能

(function() {
    'use strict';
    
    console.log('🚀 開始測試聯絡人提取功能...');
    
    // 模擬 Tampermonkey 腳本的核心功能
    function testExtractContacts() {
        const contacts = [];
        const tables = document.querySelectorAll('table');
        
        console.log(`📊 找到 ${tables.length} 個表格`);
        
        for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
            const table = tables[tableIndex];
            const rows = table.querySelectorAll('tr');
            let isContactSection = false;
            let currentDepartment = '';
            
            console.log(`🔍 檢查表格 ${tableIndex + 1}，包含 ${rows.length} 行`);
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');
                
                // 檢查是否為部門標題
                const departmentCell = row.querySelector('td[colspan="6"] b font, td[colspan="6"] font, td[colspan="6"] b');
                if (departmentCell && departmentCell.textContent.trim()) {
                    const deptText = departmentCell.textContent.trim();
                    if (!deptText.includes('Leave,') && !deptText.includes('Medical Benefits') && 
                        !deptText.includes('Housing Benefits') && deptText.length > 3) {
                        currentDepartment = deptText;
                        console.log(`🏢 找到部門: ${currentDepartment}`);
                    }
                    continue;
                }
                
                // 檢查是否為表頭行
                if (cells.length >= 5) {
                    const headerText = Array.from(cells).map(cell => cell.textContent.trim()).join(' ');
                    if (headerText.includes('Full Name') && (headerText.includes('Post') || headerText.includes('Function'))) {
                        isContactSection = true;
                        console.log(`📋 找到聯絡人表頭`);
                        continue;
                    }
                }
                
                // 提取聯絡人資料
                if (isContactSection && cells.length >= 5) {
                    const nameCell = cells[0];
                    const postCell = cells[1];
                    const phoneCell = cells[2];
                    const faxCell = cells[3];
                    const emailCell = cells[4];
                    
                    // 檢查是否為有效的聯絡人行
                    if (nameCell && nameCell.textContent.trim() && 
                        !nameCell.textContent.includes('Full Name') &&
                        nameCell.textContent.trim() !== '' &&
                        !nameCell.innerHTML.includes('&nbsp;') &&
                        nameCell.getAttribute('bgcolor') === '#FFFFFF') {
                        
                        const name = nameCell.textContent.trim();
                        const post = postCell ? postCell.textContent.trim() : '';
                        const phone = phoneCell ? phoneCell.textContent.replace(/\s+/g, ' ').trim() : '';
                        const fax = faxCell ? faxCell.textContent.trim() : '';
                        
                        // 提取電郵地址
                        let email = '';
                        if (emailCell) {
                            const emailLink = emailCell.querySelector('a[href^="mailto:"]');
                            if (emailLink) {
                                email = emailLink.textContent.trim();
                            } else {
                                const hiddenInput = emailCell.querySelector('input[name="mail"]');
                                if (hiddenInput) {
                                    email = hiddenInput.value;
                                } else {
                                    email = emailCell.textContent.trim();
                                }
                            }
                        }
                        
                        // 只添加有姓名的聯絡人
                        if (name && name.length > 0 && !name.includes('colspan')) {
                            const contact = {
                                department: currentDepartment,
                                name: name,
                                post: post,
                                phone: phone,
                                fax: fax,
                                email: email
                            };
                            contacts.push(contact);
                            console.log(`👤 提取聯絡人: ${name} (${currentDepartment})`);
                        }
                    }
                }
                
                // 如果遇到空行或發送按鈕行，重置狀態
                if (cells.length === 0 || 
                    (cells.length >= 1 && cells[0].innerHTML.includes('&nbsp;')) ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('mailSend'))) {
                    isContactSection = false;
                }
            }
        }
        
        return contacts;
    }
    
    // 將資料轉換為 CSV 格式
    function convertToCSV(contacts) {
        if (contacts.length === 0) {
            return '';
        }
        
        const headers = ['部門', '姓名', '職位/職能', '電話', '傳真', '電郵'];
        
        function escapeCSV(field) {
            if (field === null || field === undefined) {
                return '';
            }
            const str = String(field);
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                return '"' + str.replace(/"/g, '""') + '"';
            }
            return str;
        }
        
        let csv = headers.map(escapeCSV).join(',') + '\n';
        
        contacts.forEach(contact => {
            const row = [
                contact.department,
                contact.name,
                contact.post,
                contact.phone,
                contact.fax,
                contact.email
            ];
            csv += row.map(escapeCSV).join(',') + '\n';
        });
        
        return csv;
    }
    
    // 顯示測試結果
    function showTestResults(contacts) {
        console.log('\n📊 測試結果統計:');
        console.log(`總聯絡人數: ${contacts.length}`);
        
        const departments = [...new Set(contacts.map(c => c.department))].filter(d => d);
        console.log(`部門數量: ${departments.length}`);
        
        console.log('\n🏢 部門列表:');
        departments.forEach(dept => {
            const count = contacts.filter(c => c.department === dept).length;
            console.log(`  - ${dept}: ${count} 人`);
        });
        
        console.log('\n👥 聯絡人樣本 (前5個):');
        contacts.slice(0, 5).forEach((contact, index) => {
            console.log(`  ${index + 1}. ${contact.name} - ${contact.post} (${contact.email})`);
        });
        
        if (contacts.length > 5) {
            console.log(`  ... 還有 ${contacts.length - 5} 個聯絡人`);
        }
    }
    
    // 創建下載連結
    function createDownloadLink(csvContent) {
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `測試導出_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        link.textContent = '📥 下載測試 CSV 文件';
        link.style.cssText = `
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            font-weight: bold;
        `;
        
        return link;
    }
    
    // 執行測試
    function runTest() {
        try {
            console.log('🔍 開始提取聯絡人資料...');
            const contacts = testExtractContacts();
            
            if (contacts.length === 0) {
                console.warn('⚠️ 未找到任何聯絡人資料');
                return;
            }
            
            showTestResults(contacts);
            
            const csvContent = convertToCSV(contacts);
            console.log('\n📄 CSV 內容預覽 (前200字符):');
            console.log(csvContent.substring(0, 200) + '...');
            
            // 創建下載連結
            const downloadLink = createDownloadLink(csvContent);
            
            // 添加到頁面
            const container = document.createElement('div');
            container.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 9999;
                background: white;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                max-width: 400px;
            `;
            
            container.innerHTML = `
                <h3 style="margin-top: 0; color: #4CAF50;">✅ 測試完成</h3>
                <p><strong>提取聯絡人:</strong> ${contacts.length} 個</p>
                <p><strong>部門數量:</strong> ${[...new Set(contacts.map(c => c.department))].filter(d => d).length} 個</p>
                <button onclick="this.parentElement.remove()" 
                        style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; float: right;">
                    關閉
                </button>
            `;
            
            container.appendChild(downloadLink);
            document.body.appendChild(container);
            
            console.log('✅ 測試完成！查看頁面左上角的結果窗口。');
            
        } catch (error) {
            console.error('❌ 測試過程中發生錯誤:', error);
        }
    }
    
    // 延遲執行測試，確保頁面完全載入
    setTimeout(runTest, 1000);
    
})();

// 使用說明
console.log(`
🧪 聯絡人提取功能測試腳本

使用方法:
1. 在包含聯絡人資料的頁面打開瀏覽器控制台 (F12)
2. 複製並粘貼此腳本到控制台
3. 按 Enter 執行
4. 查看控制台輸出和頁面上的結果窗口

注意: 此腳本會自動在1秒後開始執行測試
`);
