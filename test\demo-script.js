// 演示腳本 - 用於測試聯絡人提取功能
// 可以在瀏覽器控制台中直接運行此腳本來測試功能

(function() {
    'use strict';
    
    console.log('🚀 開始測試聯絡人提取功能...');

    // 提取英文姓名函數
    function extractEnglishName(originalName) {
        if (!originalName || typeof originalName !== 'string') {
            return '';
        }

        const name = originalName.trim();

        // 定義稱謂列表
        const titles = ['Mr', 'Ms', 'Miss', 'Mrs', 'Dr', 'Prof'];

        // 移除稱謂
        let nameWithoutTitle = name;
        for (const title of titles) {
            if (name.startsWith(title + ' ')) {
                nameWithoutTitle = name.substring(title.length + 1).trim();
                break;
            }
        }

        // 檢查是否有逗號分隔
        if (nameWithoutTitle.includes(',')) {
            // 格式：「姓氏 姓氏, 名字」或「姓氏, 名字」
            const parts = nameWithoutTitle.split(',');
            if (parts.length >= 2) {
                const surnamesPart = parts[0].trim(); // 姓氏部分
                const givenName = parts[1].trim();    // 名字部分

                // 取姓氏的第一個單詞作為英文姓氏
                const surnames = surnamesPart.split(/\s+/);
                const englishSurname = surnames[0];

                // 組合：名字 + 姓氏
                if (givenName && englishSurname) {
                    return `${givenName} ${englishSurname}`;
                }
            }
        } else {
            // 沒有逗號的情況，可能是「姓氏 名字」格式
            const parts = nameWithoutTitle.split(/\s+/);
            if (parts.length >= 2) {
                // 假設最後一個是名字，前面的是姓氏
                const givenName = parts[parts.length - 1];
                const englishSurname = parts[0];

                // 檢查是否是重複姓氏的情況（如 "WONG WONG"）
                if (parts.length >= 2 && parts[0] === parts[1]) {
                    // 重複姓氏情況，保持原格式
                    return nameWithoutTitle;
                } else if (parts.length === 2) {
                    // 標準「姓氏 名字」格式
                    return `${givenName} ${englishSurname}`;
                }
            }
        }

        // 如果無法解析，返回去除稱謂後的原始姓名
        return nameWithoutTitle || originalName;
    }

    // 模擬 Tampermonkey 腳本的核心功能
    function testExtractContacts() {
        const contactsMap = new Map(); // 用於去重的 Map
        const tables = document.querySelectorAll('table');

        console.log(`📊 找到 ${tables.length} 個表格`);

        for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
            const table = tables[tableIndex];
            const rows = table.querySelectorAll('tr');
            let isContactSection = false;
            let currentDepartment = '';

            console.log(`🔍 檢查表格 ${tableIndex + 1}，包含 ${rows.length} 行`);

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');

                // 檢查是否為部門標題 - 精確匹配部門結構
                if (cells.length === 1 && cells[0].getAttribute('colspan') === '6') {
                    const cell = cells[0];
                    const fontElement = cell.querySelector('b font[size], font[size]');

                    if (fontElement) {
                        const deptText = fontElement.textContent.trim();

                        // 只接受真正的部門名稱（包含 Office 或 Section）
                        if ((deptText.includes('Office') || deptText.includes('Section')) &&
                            deptText.length > 10 &&
                            !deptText.includes('Opening Hours') &&
                            !deptText.includes('Email') &&
                            !deptText.includes('Fax')) {
                            currentDepartment = deptText;
                            console.log(`🏢 找到部門: ${currentDepartment}`);
                        }
                    }
                    continue;
                }
                
                // 檢查是否為表頭行
                if (cells.length >= 5) {
                    const headerText = Array.from(cells).map(cell => cell.textContent.trim()).join(' ');
                    if (headerText.includes('Full Name') && (headerText.includes('Post') || headerText.includes('Function'))) {
                        isContactSection = true;
                        console.log(`📋 找到聯絡人表頭`);
                        continue;
                    }
                }
                
                // 提取聯絡人資料
                if (isContactSection && cells.length >= 5 && currentDepartment) {
                    const nameCell = cells[0];
                    const postCell = cells[1];
                    const phoneCell = cells[2];
                    const faxCell = cells[3];
                    const emailCell = cells[4];

                    // 檢查是否為有效的聯絡人行
                    if (nameCell && nameCell.getAttribute('bgcolor') === '#FFFFFF') {
                        const name = nameCell.textContent.trim();

                        // 確保是有效的姓名
                        if (name && name.length > 0 &&
                            !name.includes('Full Name') &&
                            !name.includes('&nbsp;') &&
                            !name.includes('colspan')) {

                            const post = postCell ? postCell.textContent.trim() : '';
                            const phone = phoneCell ? phoneCell.textContent.replace(/\s+/g, ' ').trim() : '';
                            const fax = faxCell ? faxCell.textContent.trim() : '';

                            // 提取電郵地址
                            let email = '';
                            if (emailCell) {
                                const emailLink = emailCell.querySelector('a[href^="mailto:"]');
                                if (emailLink) {
                                    email = emailLink.textContent.trim();
                                } else {
                                    const hiddenInput = emailCell.querySelector('input[name="mail"]');
                                    if (hiddenInput) {
                                        email = hiddenInput.value;
                                    } else {
                                        email = emailCell.textContent.trim();
                                    }
                                }
                            }

                            // 提取英文姓名
                            const englishName = extractEnglishName(name);

                            // 創建聯絡人對象
                            const contact = {
                                department: currentDepartment,
                                name: name,
                                englishName: englishName,
                                post: post,
                                phone: phone,
                                fax: fax,
                                email: email
                            };

                            // 使用電郵作為唯一標識符進行去重
                            if (email && !contactsMap.has(email)) {
                                contactsMap.set(email, contact);
                                console.log(`👤 提取聯絡人: ${name} (${currentDepartment}) - ${email}`);
                            } else if (email && contactsMap.has(email)) {
                                console.log(`⚠️ 跳過重複聯絡人: ${name} (${currentDepartment}) - 已存在於 ${contactsMap.get(email).department}`);
                            } else if (!email) {
                                // 如果沒有電郵，使用姓名+職位作為唯一標識符
                                const uniqueKey = `${name}_${post}`;
                                if (!contactsMap.has(uniqueKey)) {
                                    contactsMap.set(uniqueKey, contact);
                                    console.log(`👤 提取聯絡人 (無電郵): ${name} (${currentDepartment})`);
                                }
                            }
                        }
                    }
                }
                
                // 如果遇到空行或發送按鈕行，重置狀態
                if (cells.length === 0 || 
                    (cells.length >= 1 && cells[0].innerHTML.includes('&nbsp;')) ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('mailSend'))) {
                    isContactSection = false;
                }
            }
        }

        // 將 Map 轉換為陣列
        const uniqueContacts = Array.from(contactsMap.values());
        console.log(`✅ 總共提取到 ${uniqueContacts.length} 個唯一聯絡人`);

        return uniqueContacts;
    }
    
    // 將資料轉換為 CSV 格式
    function convertToCSV(contacts) {
        if (contacts.length === 0) {
            return '';
        }
        
        const headers = ['部門', '姓名', '英文姓名', '職位/職能', '電話', '傳真', '電郵'];
        
        function escapeCSV(field) {
            if (field === null || field === undefined) {
                return '';
            }
            const str = String(field);
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                return '"' + str.replace(/"/g, '""') + '"';
            }
            return str;
        }
        
        let csv = headers.map(escapeCSV).join(',') + '\n';
        
        contacts.forEach(contact => {
            const row = [
                contact.department,
                contact.name,
                contact.englishName,
                contact.post,
                contact.phone,
                contact.fax,
                contact.email
            ];
            csv += row.map(escapeCSV).join(',') + '\n';
        });
        
        return csv;
    }
    
    // 顯示測試結果
    function showTestResults(contacts) {
        console.log('\n📊 測試結果統計:');
        console.log(`總聯絡人數: ${contacts.length}`);
        
        const departments = [...new Set(contacts.map(c => c.department))].filter(d => d);
        console.log(`部門數量: ${departments.length}`);
        
        console.log('\n🏢 部門列表:');
        departments.forEach(dept => {
            const count = contacts.filter(c => c.department === dept).length;
            console.log(`  - ${dept}: ${count} 人`);
        });
        
        console.log('\n👥 聯絡人樣本 (前5個):');
        contacts.slice(0, 5).forEach((contact, index) => {
            console.log(`  ${index + 1}. ${contact.name} - ${contact.post} (${contact.email})`);
        });
        
        if (contacts.length > 5) {
            console.log(`  ... 還有 ${contacts.length - 5} 個聯絡人`);
        }
    }
    
    // 創建下載連結
    function createDownloadLink(csvContent) {
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `測試導出_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        link.textContent = '📥 下載測試 CSV 文件';
        link.style.cssText = `
            display: inline-block;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            font-weight: bold;
        `;
        
        return link;
    }
    
    // 執行測試
    function runTest() {
        try {
            console.log('🔍 開始提取聯絡人資料...');
            const contacts = testExtractContacts();
            
            if (contacts.length === 0) {
                console.warn('⚠️ 未找到任何聯絡人資料');
                return;
            }
            
            showTestResults(contacts);
            
            const csvContent = convertToCSV(contacts);
            console.log('\n📄 CSV 內容預覽 (前200字符):');
            console.log(csvContent.substring(0, 200) + '...');
            
            // 創建下載連結
            const downloadLink = createDownloadLink(csvContent);
            
            // 添加到頁面
            const container = document.createElement('div');
            container.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 9999;
                background: white;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                max-width: 400px;
            `;
            
            container.innerHTML = `
                <h3 style="margin-top: 0; color: #4CAF50;">✅ 測試完成</h3>
                <p><strong>提取聯絡人:</strong> ${contacts.length} 個</p>
                <p><strong>部門數量:</strong> ${[...new Set(contacts.map(c => c.department))].filter(d => d).length} 個</p>
                <button onclick="this.parentElement.remove()" 
                        style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; float: right;">
                    關閉
                </button>
            `;
            
            container.appendChild(downloadLink);
            document.body.appendChild(container);
            
            console.log('✅ 測試完成！查看頁面左上角的結果窗口。');
            
        } catch (error) {
            console.error('❌ 測試過程中發生錯誤:', error);
        }
    }
    
    // 延遲執行測試，確保頁面完全載入
    setTimeout(runTest, 1000);
    
})();

// 使用說明
console.log(`
🧪 聯絡人提取功能測試腳本

使用方法:
1. 在包含聯絡人資料的頁面打開瀏覽器控制台 (F12)
2. 複製並粘貼此腳本到控制台
3. 按 Enter 執行
4. 查看控制台輸出和頁面上的結果窗口

注意: 此腳本會自動在1秒後開始執行測試
`);
