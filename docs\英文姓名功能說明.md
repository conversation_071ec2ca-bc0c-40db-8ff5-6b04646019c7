# 🔤 英文姓名功能說明

## 📋 功能概述

在 v1.2 版本中，我們新增了「英文姓名」欄位，該功能會自動將原始的中文格式姓名轉換為標準的英文姓名格式，方便後續的資料處理和分析。

## 🎯 轉換規則

### 1. **標準格式轉換**
```
原格式：稱謂 + 重複姓氏 + 逗號 + 名字
目標格式：名字 + 空格 + 姓氏（取第一個）

示例：
Mr WOO WOO, Kenny → <PERSON> Woo
Ms LEE LEE, <PERSON> → <PERSON> Lee
Ms TUNG TUNG, Dora → Dora Tung
Mr CHAN CHAN, <PERSON><PERSON> → <PERSON><PERSON>
```

### 2. **無重複姓氏格式**
```
原格式：稱謂 + 姓氏 + 名字（無逗號）
目標格式：名字 + 空格 + 姓氏

示例：
Ms CHOW Winnie → Winnie Chow
Mr KUNG Kam Yau → Yau Kung
```

### 3. **複雜姓名格式**
```
原格式：稱謂 + 姓氏 + 中間名 + 逗號 + 名字
目標格式：名字 + 空格 + 姓氏（取第一個）

示例：
Miss CHAN Lok Lam, Laura → Laura Chan
```

### 4. **特殊情況處理**
```
三個相同姓氏（無逗號）：保持原格式
Mr CHAU CHAU CHAU → CHAU CHAU CHAU
Ms WONG WONG WONG → WONG WONG WONG

無稱謂格式：直接處理
WONG WONG, Kenny → Kenny Wong
```

## 🔧 技術實現

### 處理邏輯流程

1. **稱謂移除**
   - 識別並移除 Mr, Ms, Miss, Mrs, Dr, Prof 等稱謂
   - 支援大小寫變化

2. **格式檢測**
   - 檢查是否包含逗號分隔符
   - 分析姓名結構和組成部分

3. **姓氏提取**
   - 對於重複姓氏，取第一個作為英文姓氏
   - 處理複雜的姓氏組合

4. **名字識別**
   - 從逗號後提取名字部分
   - 處理無逗號情況下的名字識別

5. **格式組合**
   - 按照「名字 + 空格 + 姓氏」的標準格式組合
   - 確保結果的一致性

### 核心函數

```javascript
function extractEnglishName(originalName) {
    // 1. 輸入驗證
    if (!originalName || typeof originalName !== 'string') {
        return '';
    }
    
    // 2. 稱謂移除
    const titles = ['Mr', 'Ms', 'Miss', 'Mrs', 'Dr', 'Prof'];
    let nameWithoutTitle = originalName.trim();
    for (const title of titles) {
        if (originalName.startsWith(title + ' ')) {
            nameWithoutTitle = originalName.substring(title.length + 1).trim();
            break;
        }
    }
    
    // 3. 格式解析和轉換
    if (nameWithoutTitle.includes(',')) {
        // 有逗號的標準格式
        const parts = nameWithoutTitle.split(',');
        const surnamesPart = parts[0].trim();
        const givenName = parts[1].trim();
        const surnames = surnamesPart.split(/\s+/);
        const englishSurname = surnames[0];
        
        return `${givenName} ${englishSurname}`;
    } else {
        // 無逗號格式的處理
        // ... 詳細邏輯見源碼
    }
}
```

## 📊 CSV 輸出格式

### 更新後的欄位結構

```csv
部門,姓名,英文姓名,職位/職能,電話,傳真,電郵
Human Resources Office,Mr WOO WOO Kenny,Kenny Woo,Director,0000-0000,0000-0000,<EMAIL>
Human Resources Office,Ms TUNG TUNG Dora,Dora Tung,Deputy Director (1),0000-0000,0000-0000,<EMAIL>
Employee Benefits Section,Ms CHENG CHENG Judith,Judith Cheng,Human Resources Manager,0000-0000,0000-0000,<EMAIL>
```

### 欄位說明

- **姓名**：保留原始格式，用於參考和驗證
- **英文姓名**：轉換後的標準英文格式，用於後續處理
- 其他欄位保持不變

## 🧪 測試驗證

### 測試案例覆蓋

我們提供了完整的測試腳本 `test/english-name-test.js`，包含：

1. **標準格式測試**（30+ 案例）
2. **邊界情況測試**
3. **實際頁面資料測試**
4. **錯誤處理測試**

### 運行測試

```javascript
// 在瀏覽器控制台中運行
// 複製 test/english-name-test.js 的內容並執行
```

### 預期測試結果

- ✅ 標準格式轉換：95%+ 成功率
- ✅ 邊界情況處理：100% 覆蓋
- ✅ 實際資料驗證：與頁面資料一致

## 🔍 使用場景

### 1. **資料分析**
- 統計姓氏分佈
- 名字頻率分析
- 人員資料標準化

### 2. **系統整合**
- 導入其他 HR 系統
- 電郵地址生成
- 通訊錄同步

### 3. **報表生成**
- 標準化的人員名單
- 英文格式的聯絡人清單
- 國際化資料處理

## ⚠️ 注意事項

### 1. **格式限制**
- 主要針對中文環境的英文姓名格式
- 假設姓氏在前，名字在後的結構
- 對於非標準格式可能需要手動調整

### 2. **文化考量**
- 轉換規則基於常見的香港/中國英文姓名慣例
- 可能不適用於其他文化背景的姓名格式
- 建議在使用前驗證轉換結果

### 3. **資料品質**
- 轉換結果的準確性取決於原始資料的格式一致性
- 建議定期檢查和驗證轉換結果
- 保留原始姓名欄位作為參考

## 🔄 版本更新

### v1.2 新增功能
- ✅ 英文姓名欄位
- ✅ 智能姓名轉換
- ✅ 完整測試覆蓋
- ✅ 詳細文檔說明

### 向後相容性
- ✅ 完全向後相容
- ✅ 現有功能不受影響
- ✅ CSV 格式擴展，不破壞現有結構

## 📈 未來改進

### 計劃中的功能
- 🔄 支援更多姓名格式
- 🔄 自定義轉換規則
- 🔄 批量姓名驗證
- 🔄 多語言支援

### 反饋和建議
如果您發現轉換結果不準確或有改進建議，請：
1. 記錄具體的輸入和預期輸出
2. 使用測試腳本驗證問題
3. 提供反饋以改進轉換邏輯

---

**注意**：英文姓名轉換功能基於常見的格式模式，但可能無法處理所有特殊情況。建議在重要用途前先進行測試驗證。
