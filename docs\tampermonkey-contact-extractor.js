// ==UserScript==
// @name         聯絡人目錄 CSV 導出器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  從電子電話目錄網站提取聯絡人資料並導出為 CSV 格式
// <AUTHOR>
// @match        *://intra.abc.edu.hk/directory/*
// @match        *://*/directory/*
// @match        file://*raw.html*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 檢查是否為聯絡人目錄頁面
    function isContactDirectoryPage() {
        const title = document.title;
        // 檢查是否有聯絡人表格的特徵
        const hasContactTable = document.querySelector('th') &&
                               (document.body.innerHTML.includes('Full Name') ||
                                document.body.innerHTML.includes('Post / Function') ||
                                document.body.innerHTML.includes('Electronic Telephone Directory'));
        return title.includes('Electronic Telephone Directory') || hasContactTable;
    }

    // 創建導出按鈕
    function createExportButton() {
        const button = document.createElement('button');
        button.innerHTML = '📥 導出聯絡人為 CSV';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;
        
        button.addEventListener('mouseenter', function() {
            this.style.background = '#45a049';
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.background = '#4CAF50';
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', extractAndExportContacts);
        
        return button;
    }

    // 提取聯絡人資料
    function extractContacts() {
        const contacts = [];
        const tables = document.querySelectorAll('table');

        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            let isContactSection = false;
            let currentDepartment = '';

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');

                // 檢查是否為部門標題 - 更靈活的匹配
                const departmentCell = row.querySelector('td[colspan="6"] b font, td[colspan="6"] font, td[colspan="6"] b');
                if (departmentCell && departmentCell.textContent.trim()) {
                    const deptText = departmentCell.textContent.trim();
                    // 過濾掉一些非部門的文字
                    if (!deptText.includes('Leave,') && !deptText.includes('Medical Benefits') &&
                        !deptText.includes('Housing Benefits') && deptText.length > 3) {
                        currentDepartment = deptText;
                    }
                    continue;
                }

                // 檢查是否為表頭行
                if (cells.length >= 5) {
                    const headerText = Array.from(cells).map(cell => cell.textContent.trim()).join(' ');
                    if (headerText.includes('Full Name') && (headerText.includes('Post') || headerText.includes('Function'))) {
                        isContactSection = true;
                        continue;
                    }
                }

                // 提取聯絡人資料
                if (isContactSection && cells.length >= 5) {
                    const nameCell = cells[0];
                    const postCell = cells[1];
                    const phoneCell = cells[2];
                    const faxCell = cells[3];
                    const emailCell = cells[4];

                    // 檢查是否為有效的聯絡人行
                    if (nameCell && nameCell.textContent.trim() &&
                        !nameCell.textContent.includes('Full Name') &&
                        nameCell.textContent.trim() !== '' &&
                        !nameCell.innerHTML.includes('&nbsp;') &&
                        nameCell.getAttribute('bgcolor') === '#FFFFFF') {

                        const name = nameCell.textContent.trim();
                        const post = postCell ? postCell.textContent.trim() : '';
                        const phone = phoneCell ? phoneCell.textContent.replace(/\s+/g, ' ').trim() : '';
                        const fax = faxCell ? faxCell.textContent.trim() : '';

                        // 提取電郵地址
                        let email = '';
                        if (emailCell) {
                            const emailLink = emailCell.querySelector('a[href^="mailto:"]');
                            if (emailLink) {
                                email = emailLink.textContent.trim();
                            } else {
                                const hiddenInput = emailCell.querySelector('input[name="mail"]');
                                if (hiddenInput) {
                                    email = hiddenInput.value;
                                } else {
                                    email = emailCell.textContent.trim();
                                }
                            }
                        }

                        // 只添加有姓名的聯絡人
                        if (name && name.length > 0 && !name.includes('colspan')) {
                            contacts.push({
                                department: currentDepartment,
                                name: name,
                                post: post,
                                phone: phone,
                                fax: fax,
                                email: email
                            });
                        }
                    }
                }

                // 如果遇到空行或發送按鈕行，重置狀態
                if (cells.length === 0 ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('&nbsp;')) ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('mailSend'))) {
                    isContactSection = false;
                }
            }
        }

        return contacts;
    }

    // 將資料轉換為 CSV 格式
    function convertToCSV(contacts) {
        if (contacts.length === 0) {
            return '';
        }
        
        // CSV 標題行
        const headers = ['部門', '姓名', '職位/職能', '電話', '傳真', '電郵'];
        
        // 轉義 CSV 特殊字符
        function escapeCSV(field) {
            if (field === null || field === undefined) {
                return '';
            }
            const str = String(field);
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                return '"' + str.replace(/"/g, '""') + '"';
            }
            return str;
        }
        
        // 構建 CSV 內容
        let csv = headers.map(escapeCSV).join(',') + '\n';
        
        contacts.forEach(contact => {
            const row = [
                contact.department,
                contact.name,
                contact.post,
                contact.phone,
                contact.fax,
                contact.email
            ];
            csv += row.map(escapeCSV).join(',') + '\n';
        });
        
        return csv;
    }

    // 下載 CSV 文件
    function downloadCSV(csvContent, filename) {
        // 添加 BOM 以支持中文字符
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        const link = document.createElement('a');
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }

    // 顯示結果統計
    function showStats(contacts) {
        const departments = [...new Set(contacts.map(c => c.department))].filter(d => d);
        
        const statsDiv = document.createElement('div');
        statsDiv.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 9998;
            background: #f0f8ff;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            max-width: 300px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        `;
        
        statsDiv.innerHTML = `
            <div style="font-weight: bold; color: #4CAF50; margin-bottom: 10px;">
                ✅ 導出成功！
            </div>
            <div><strong>總聯絡人數：</strong> ${contacts.length}</div>
            <div><strong>部門數量：</strong> ${departments.length}</div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <strong>包含部門：</strong><br>
                ${departments.slice(0, 5).join('<br>')}
                ${departments.length > 5 ? '<br>...' : ''}
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="margin-top: 10px; background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                關閉
            </button>
        `;
        
        document.body.appendChild(statsDiv);
        
        // 5秒後自動移除
        setTimeout(() => {
            if (statsDiv.parentElement) {
                statsDiv.remove();
            }
        }, 5000);
    }

    // 主要導出函數
    function extractAndExportContacts() {
        try {
            const contacts = extractContacts();
            
            if (contacts.length === 0) {
                alert('未找到聯絡人資料。請確保您在正確的聯絡人目錄頁面上。');
                return;
            }
            
            const csvContent = convertToCSV(contacts);
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `聯絡人目錄_${timestamp}.csv`;
            
            downloadCSV(csvContent, filename);
            showStats(contacts);
            
        } catch (error) {
            console.error('導出聯絡人時發生錯誤:', error);
            alert('導出過程中發生錯誤：' + error.message);
        }
    }

    // 初始化腳本
    function init() {
        if (isContactDirectoryPage()) {
            const exportButton = createExportButton();
            document.body.appendChild(exportButton);
            
            console.log('聯絡人目錄 CSV 導出器已載入');
        }
    }

    // 等待頁面載入完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
