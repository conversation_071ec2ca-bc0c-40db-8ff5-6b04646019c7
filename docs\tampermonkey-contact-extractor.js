// ==UserScript==
// @name         聯絡人目錄 CSV 導出器
// @namespace    http://tampermonkey.net/
// @version      1.2
// @description  從電子電話目錄網站提取聯絡人資料並導出為 CSV 格式 - 新增英文姓名欄位
// <AUTHOR>
// @match        *://intra.abc.edu.hk/directory/*
// @match        *://*/directory/*
// @match        *raw.html*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 檢查是否為聯絡人目錄頁面
    function isContactDirectoryPage() {
        const title = document.title;
        // 檢查是否有聯絡人表格的特徵
        const hasContactTable = document.querySelector('th') &&
                               (document.body.innerHTML.includes('Full Name') ||
                                document.body.innerHTML.includes('Post / Function') ||
                                document.body.innerHTML.includes('Electronic Telephone Directory'));
        return title.includes('Electronic Telephone Directory') || hasContactTable;
    }

    // 創建導出按鈕
    function createExportButton() {
        const button = document.createElement('button');
        button.innerHTML = '📥 導出聯絡人為 CSV';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;
        
        button.addEventListener('mouseenter', function() {
            this.style.background = '#45a049';
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.background = '#4CAF50';
            this.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('click', extractAndExportContacts);
        
        return button;
    }

    // 提取英文姓名函數
    function extractEnglishName(originalName) {
        if (!originalName || typeof originalName !== 'string') {
            return '';
        }

        const name = originalName.trim();

        // 定義稱謂列表
        const titles = ['Mr', 'Ms', 'Miss', 'Mrs', 'Dr', 'Prof'];

        // 移除稱謂
        let nameWithoutTitle = name;
        for (const title of titles) {
            if (name.startsWith(title + ' ')) {
                nameWithoutTitle = name.substring(title.length + 1).trim();
                break;
            }
        }

        // 檢查是否有逗號分隔
        if (nameWithoutTitle.includes(',')) {
            // 格式：「姓氏 姓氏, 名字」或「姓氏, 名字」
            const parts = nameWithoutTitle.split(',');
            if (parts.length >= 2) {
                const surnamesPart = parts[0].trim(); // 姓氏部分
                const givenName = parts[1].trim();    // 名字部分

                // 取姓氏的第一個單詞作為英文姓氏
                const surnames = surnamesPart.split(/\s+/);
                const englishSurname = surnames[0];

                // 組合：名字 + 姓氏
                if (givenName && englishSurname) {
                    return `${givenName} ${englishSurname}`;
                }
            }
        } else {
            // 沒有逗號的情況，可能是「姓氏 名字」格式
            const parts = nameWithoutTitle.split(/\s+/);
            if (parts.length >= 2) {
                // 假設最後一個是名字，前面的是姓氏
                const givenName = parts[parts.length - 1];
                const englishSurname = parts[0];

                // 檢查是否是重複姓氏的情況（如 "WONG WONG"）
                if (parts.length >= 2 && parts[0] === parts[1]) {
                    // 重複姓氏情況，保持原格式
                    return nameWithoutTitle;
                } else if (parts.length === 2) {
                    // 標準「姓氏 名字」格式
                    return `${givenName} ${englishSurname}`;
                }
            }
        }

        // 如果無法解析，返回去除稱謂後的原始姓名
        return nameWithoutTitle || originalName;
    }

    // 提取聯絡人資料
    function extractContacts() {
        const contactsMap = new Map(); // 用於去重的 Map，key 為 email，value 為聯絡人資料
        const tables = document.querySelectorAll('table');

        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            let isContactSection = false;
            let currentDepartment = '';

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');

                // 檢查是否為部門標題 - 精確匹配部門結構
                // 部門標題的結構：<td colspan="6" align="center"><b><font>部門名稱</font></b></td>
                if (cells.length === 1 && cells[0].getAttribute('colspan') === '6') {
                    const cell = cells[0];
                    const fontElement = cell.querySelector('b font[size], font[size]');

                    if (fontElement) {
                        const deptText = fontElement.textContent.trim();

                        // 只接受真正的部門名稱（包含 Office 或 Section）
                        if ((deptText.includes('Office') || deptText.includes('Section')) &&
                            deptText.length > 10 &&
                            !deptText.includes('Opening Hours') &&
                            !deptText.includes('Email') &&
                            !deptText.includes('Fax')) {
                            currentDepartment = deptText;
                            console.log(`找到部門: ${currentDepartment}`);
                        }
                    }
                    continue;
                }

                // 檢查是否為表頭行
                if (cells.length >= 5) {
                    const headerText = Array.from(cells).map(cell => cell.textContent.trim()).join(' ');
                    if (headerText.includes('Full Name') && (headerText.includes('Post') || headerText.includes('Function'))) {
                        isContactSection = true;
                        console.log(`在部門 "${currentDepartment}" 中找到聯絡人表頭`);
                        continue;
                    }
                }

                // 提取聯絡人資料
                if (isContactSection && cells.length >= 5 && currentDepartment) {
                    const nameCell = cells[0];
                    const postCell = cells[1];
                    const phoneCell = cells[2];
                    const faxCell = cells[3];
                    const emailCell = cells[4];

                    // 檢查是否為有效的聯絡人行
                    if (nameCell && nameCell.getAttribute('bgcolor') === '#FFFFFF') {
                        const name = nameCell.textContent.trim();

                        // 確保是有效的姓名（不是空白或表頭）
                        if (name && name.length > 0 &&
                            !name.includes('Full Name') &&
                            !name.includes('&nbsp;') &&
                            !name.includes('colspan')) {

                            const post = postCell ? postCell.textContent.trim() : '';
                            const phone = phoneCell ? phoneCell.textContent.replace(/\s+/g, ' ').trim() : '';
                            const fax = faxCell ? faxCell.textContent.trim() : '';

                            // 提取電郵地址
                            let email = '';
                            if (emailCell) {
                                const emailLink = emailCell.querySelector('a[href^="mailto:"]');
                                if (emailLink) {
                                    email = emailLink.textContent.trim();
                                } else {
                                    const hiddenInput = emailCell.querySelector('input[name="mail"]');
                                    if (hiddenInput) {
                                        email = hiddenInput.value;
                                    } else {
                                        email = emailCell.textContent.trim();
                                    }
                                }
                            }

                            // 提取英文姓名
                            const englishName = extractEnglishName(name);

                            // 創建聯絡人對象
                            const contact = {
                                department: currentDepartment,
                                name: name,
                                englishName: englishName,
                                post: post,
                                phone: phone,
                                fax: fax,
                                email: email
                            };

                            // 使用電郵作為唯一標識符進行去重
                            // 如果同一個人在多個部門，優先保留第一次出現的部門
                            if (email && !contactsMap.has(email)) {
                                contactsMap.set(email, contact);
                                console.log(`提取聯絡人: ${name} (${currentDepartment}) - ${email}`);
                            } else if (email && contactsMap.has(email)) {
                                console.log(`跳過重複聯絡人: ${name} (${currentDepartment}) - 已存在於 ${contactsMap.get(email).department}`);
                            } else if (!email) {
                                // 如果沒有電郵，使用姓名+職位作為唯一標識符
                                const uniqueKey = `${name}_${post}`;
                                if (!contactsMap.has(uniqueKey)) {
                                    contactsMap.set(uniqueKey, contact);
                                    console.log(`提取聯絡人 (無電郵): ${name} (${currentDepartment})`);
                                }
                            }
                        }
                    }
                }

                // 如果遇到空行或發送按鈕行，重置聯絡人區段狀態
                if (cells.length === 0 ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('&nbsp;')) ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('mailSend'))) {
                    isContactSection = false;
                }
            }
        }

        // 將 Map 轉換為陣列
        const uniqueContacts = Array.from(contactsMap.values());
        console.log(`總共提取到 ${uniqueContacts.length} 個唯一聯絡人`);

        return uniqueContacts;
    }

    // 將資料轉換為 CSV 格式
    function convertToCSV(contacts) {
        if (contacts.length === 0) {
            return '';
        }
        
        // CSV 標題行
        const headers = ['部門', '姓名', '英文姓名', '職位/職能', '電話', '傳真', '電郵'];
        
        // 轉義 CSV 特殊字符
        function escapeCSV(field) {
            if (field === null || field === undefined) {
                return '';
            }
            const str = String(field);
            if (str.includes(',') || str.includes('"') || str.includes('\n')) {
                return '"' + str.replace(/"/g, '""') + '"';
            }
            return str;
        }
        
        // 構建 CSV 內容
        let csv = headers.map(escapeCSV).join(',') + '\n';
        
        contacts.forEach(contact => {
            const row = [
                contact.department,
                contact.name,
                contact.englishName,
                contact.post,
                contact.phone,
                contact.fax,
                contact.email
            ];
            csv += row.map(escapeCSV).join(',') + '\n';
        });
        
        return csv;
    }

    // 下載 CSV 文件
    function downloadCSV(csvContent, filename) {
        // 添加 BOM 以支持中文字符
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
        
        const link = document.createElement('a');
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }

    // 顯示結果統計
    function showStats(contacts) {
        const departments = [...new Set(contacts.map(c => c.department))].filter(d => d);
        
        const statsDiv = document.createElement('div');
        statsDiv.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 9998;
            background: #f0f8ff;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            max-width: 300px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        `;
        
        statsDiv.innerHTML = `
            <div style="font-weight: bold; color: #4CAF50; margin-bottom: 10px;">
                ✅ 導出成功！
            </div>
            <div><strong>總聯絡人數：</strong> ${contacts.length}</div>
            <div><strong>部門數量：</strong> ${departments.length}</div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                <strong>包含部門：</strong><br>
                ${departments.slice(0, 5).join('<br>')}
                ${departments.length > 5 ? '<br>...' : ''}
            </div>
            <button onclick="this.parentElement.remove()" 
                    style="margin-top: 10px; background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                關閉
            </button>
        `;
        
        document.body.appendChild(statsDiv);
        
        // 5秒後自動移除
        setTimeout(() => {
            if (statsDiv.parentElement) {
                statsDiv.remove();
            }
        }, 5000);
    }

    // 主要導出函數
    function extractAndExportContacts() {
        try {
            const contacts = extractContacts();
            
            if (contacts.length === 0) {
                alert('未找到聯絡人資料。請確保您在正確的聯絡人目錄頁面上。');
                return;
            }
            
            const csvContent = convertToCSV(contacts);
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `聯絡人目錄_${timestamp}.csv`;
            
            downloadCSV(csvContent, filename);
            showStats(contacts);
            
        } catch (error) {
            console.error('導出聯絡人時發生錯誤:', error);
            alert('導出過程中發生錯誤：' + error.message);
        }
    }

    // 初始化腳本
    function init() {
        if (isContactDirectoryPage()) {
            const exportButton = createExportButton();
            document.body.appendChild(exportButton);
            
            console.log('聯絡人目錄 CSV 導出器已載入');
        }
    }

    // 等待頁面載入完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
