// 驗證腳本 - 用於測試修復後的功能
// 在瀏覽器控制台中運行此腳本來驗證修復效果

(function() {
    'use strict';
    
    console.log('🔍 開始驗證聯絡人提取修復效果...');
    
    // 1. 驗證部門識別
    function validateDepartmentExtraction() {
        console.log('\n📋 1. 驗證部門識別功能...');
        
        const departmentElements = document.querySelectorAll('td[colspan="6"] b font[size], td[colspan="6"] font[size]');
        const validDepartments = [];
        const invalidDepartments = [];
        
        departmentElements.forEach(el => {
            const deptText = el.textContent.trim();
            
            if ((deptText.includes('Office') || deptText.includes('Section')) && 
                deptText.length > 10 && 
                !deptText.includes('Opening Hours') && 
                !deptText.includes('Email') &&
                !deptText.includes('Fax')) {
                validDepartments.push(deptText);
                console.log(`✅ 有效部門: ${deptText}`);
            } else {
                invalidDepartments.push(deptText);
                console.log(`❌ 無效部門 (已過濾): ${deptText}`);
            }
        });
        
        console.log(`\n📊 部門識別結果:`);
        console.log(`  - 有效部門: ${validDepartments.length} 個`);
        console.log(`  - 已過濾: ${invalidDepartments.length} 個`);
        
        return validDepartments;
    }
    
    // 2. 驗證聯絡人提取和去重
    function validateContactExtraction() {
        console.log('\n👥 2. 驗證聯絡人提取和去重功能...');
        
        const contactsMap = new Map();
        const duplicateEmails = [];
        const tables = document.querySelectorAll('table');
        
        for (let table of tables) {
            const rows = table.querySelectorAll('tr');
            let isContactSection = false;
            let currentDepartment = '';
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td, th');
                
                // 檢查部門標題
                if (cells.length === 1 && cells[0].getAttribute('colspan') === '6') {
                    const cell = cells[0];
                    const fontElement = cell.querySelector('b font[size], font[size]');
                    
                    if (fontElement) {
                        const deptText = fontElement.textContent.trim();
                        
                        if ((deptText.includes('Office') || deptText.includes('Section')) && 
                            deptText.length > 10 && 
                            !deptText.includes('Opening Hours') && 
                            !deptText.includes('Email') &&
                            !deptText.includes('Fax')) {
                            currentDepartment = deptText;
                        }
                    }
                    continue;
                }
                
                // 檢查表頭
                if (cells.length >= 5) {
                    const headerText = Array.from(cells).map(cell => cell.textContent.trim()).join(' ');
                    if (headerText.includes('Full Name') && (headerText.includes('Post') || headerText.includes('Function'))) {
                        isContactSection = true;
                        continue;
                    }
                }
                
                // 提取聯絡人
                if (isContactSection && cells.length >= 5 && currentDepartment) {
                    const nameCell = cells[0];
                    const emailCell = cells[4];
                    
                    if (nameCell && nameCell.getAttribute('bgcolor') === '#FFFFFF') {
                        const name = nameCell.textContent.trim();
                        
                        if (name && name.length > 0 && 
                            !name.includes('Full Name') &&
                            !name.includes('&nbsp;') &&
                            !name.includes('colspan')) {
                            
                            // 提取電郵
                            let email = '';
                            if (emailCell) {
                                const emailLink = emailCell.querySelector('a[href^="mailto:"]');
                                if (emailLink) {
                                    email = emailLink.textContent.trim();
                                } else {
                                    const hiddenInput = emailCell.querySelector('input[name="mail"]');
                                    if (hiddenInput) {
                                        email = hiddenInput.value;
                                    }
                                }
                            }
                            
                            // 檢查去重
                            if (email) {
                                if (contactsMap.has(email)) {
                                    duplicateEmails.push({
                                        email: email,
                                        name: name,
                                        department: currentDepartment,
                                        existingDepartment: contactsMap.get(email).department
                                    });
                                    console.log(`🔄 發現重複: ${name} (${currentDepartment}) - 已存在於 ${contactsMap.get(email).department}`);
                                } else {
                                    contactsMap.set(email, {
                                        name: name,
                                        department: currentDepartment,
                                        email: email
                                    });
                                }
                            }
                        }
                    }
                }
                
                // 重置狀態
                if (cells.length === 0 || 
                    (cells.length >= 1 && cells[0].innerHTML.includes('&nbsp;')) ||
                    (cells.length >= 1 && cells[0].innerHTML.includes('mailSend'))) {
                    isContactSection = false;
                }
            }
        }
        
        console.log(`\n📊 聯絡人提取結果:`);
        console.log(`  - 唯一聯絡人: ${contactsMap.size} 個`);
        console.log(`  - 發現重複: ${duplicateEmails.length} 個`);
        
        return { uniqueContacts: contactsMap, duplicates: duplicateEmails };
    }
    
    // 3. 驗證特定問題案例
    function validateSpecificCases() {
        console.log('\n🎯 3. 驗證特定問題案例...');
        
        // 檢查 Ms SHIU SHIU, Crystal 的重複問題
        const crystalElements = document.querySelectorAll('td[bgcolor="#FFFFFF"]');
        const crystalOccurrences = [];
        
        crystalElements.forEach(el => {
            if (el.textContent.includes('Ms SHIU SHIU, Crystal')) {
                // 找到所屬部門
                let currentRow = el.parentElement;
                let department = '未知部門';
                
                // 向上查找部門標題
                while (currentRow && currentRow.previousElementSibling) {
                    currentRow = currentRow.previousElementSibling;
                    const deptCell = currentRow.querySelector('td[colspan="6"] b font[size], td[colspan="6"] font[size]');
                    if (deptCell) {
                        const deptText = deptCell.textContent.trim();
                        if ((deptText.includes('Office') || deptText.includes('Section')) && deptText.length > 10) {
                            department = deptText;
                            break;
                        }
                    }
                }
                
                crystalOccurrences.push(department);
            }
        });
        
        console.log(`🔍 Ms SHIU SHIU, Crystal 出現次數: ${crystalOccurrences.length}`);
        crystalOccurrences.forEach((dept, index) => {
            console.log(`  ${index + 1}. ${dept}`);
        });
        
        if (crystalOccurrences.length > 1) {
            console.log(`⚠️ 確認存在重複問題，去重機制應該處理此情況`);
        }
        
        return crystalOccurrences;
    }
    
    // 4. 生成驗證報告
    function generateValidationReport(departments, contacts, crystalCases) {
        console.log('\n📋 4. 生成驗證報告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            departments: {
                total: departments.length,
                list: departments
            },
            contacts: {
                unique: contacts.uniqueContacts.size,
                duplicates: contacts.duplicates.length,
                duplicateDetails: contacts.duplicates
            },
            specificCases: {
                crystalOccurrences: crystalCases.length,
                departments: crystalCases
            },
            validation: {
                departmentExtractionFixed: departments.length > 0 && !departments.some(d => d.includes('Opening Hours') || d.includes('Email')),
                deduplicationWorking: contacts.duplicates.length > 0, // 如果發現重複，說明去重機制在工作
                crystalCaseHandled: crystalCases.length > 1 // 確認存在需要去重的案例
            }
        };
        
        console.log('\n📊 最終驗證報告:');
        console.log(`✅ 部門識別修復: ${report.validation.departmentExtractionFixed ? '成功' : '失敗'}`);
        console.log(`✅ 去重機制工作: ${report.validation.deduplicationWorking ? '正常' : '需檢查'}`);
        console.log(`✅ 特定案例處理: ${report.validation.crystalCaseHandled ? '已識別' : '未發現'}`);
        
        // 創建下載連結
        const reportJson = JSON.stringify(report, null, 2);
        const blob = new Blob([reportJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `驗證報告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        link.textContent = '📥 下載驗證報告 (JSON)';
        link.style.cssText = `
            display: inline-block;
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            font-weight: bold;
        `;
        
        // 添加到頁面
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            background: white;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            max-width: 500px;
        `;
        
        container.innerHTML = `
            <h3 style="margin-top: 0; color: #2196F3;">🔍 驗證完成</h3>
            <p><strong>部門識別:</strong> ${report.validation.departmentExtractionFixed ? '✅ 修復成功' : '❌ 仍有問題'}</p>
            <p><strong>去重機制:</strong> ${report.validation.deduplicationWorking ? '✅ 正常工作' : '⚠️ 需檢查'}</p>
            <p><strong>唯一聯絡人:</strong> ${report.contacts.unique} 個</p>
            <p><strong>發現重複:</strong> ${report.contacts.duplicates} 個</p>
            <button onclick="this.parentElement.remove()" 
                    style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; float: right;">
                關閉
            </button>
        `;
        
        container.appendChild(link);
        document.body.appendChild(container);
        
        return report;
    }
    
    // 執行驗證
    function runValidation() {
        try {
            const departments = validateDepartmentExtraction();
            const contacts = validateContactExtraction();
            const crystalCases = validateSpecificCases();
            const report = generateValidationReport(departments, contacts, crystalCases);
            
            console.log('\n🎉 驗證完成！查看頁面左上角的結果窗口。');
            return report;
            
        } catch (error) {
            console.error('❌ 驗證過程中發生錯誤:', error);
        }
    }
    
    // 延遲執行驗證
    setTimeout(runValidation, 1000);
    
})();

console.log(`
🔍 聯絡人提取修復驗證腳本

此腳本將驗證以下修復:
1. ✅ 部門識別是否正確 (不再包含 "Opening Hours", "Email" 等)
2. ✅ 去重機制是否工作 (重複聯絡人是否被正確處理)
3. ✅ 特定案例是否修復 (如 Ms SHIU SHIU, Crystal 的重複問題)

腳本將在1秒後自動開始驗證...
`);
