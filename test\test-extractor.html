<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聯絡人目錄 CSV 導出器 - 測試頁面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #4CAF50;
            margin-top: 0;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .step {
            margin: 10px 0;
            padding: 8px 12px;
            background: #f0f8ff;
            border-left: 4px solid #4CAF50;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 聯絡人目錄 CSV 導出器 - 測試頁面</h1>
        
        <div class="instructions">
            <h3>📋 測試說明</h3>
            <p>此頁面用於測試 Tampermonkey 聯絡人目錄 CSV 導出器的功能。請按照以下步驟進行測試：</p>
        </div>

        <div class="test-section">
            <h2>🔧 準備工作</h2>
            
            <div class="step">
                <strong>步驟 1：</strong> 確保已安裝 Tampermonkey 瀏覽器擴展
            </div>
            
            <div class="step">
                <strong>步驟 2：</strong> 在 Tampermonkey 中安裝聯絡人目錄 CSV 導出器腳本
            </div>
            
            <div class="step">
                <strong>步驟 3：</strong> 確保腳本已啟用並匹配當前頁面
            </div>
            
            <div class="warning">
                <strong>⚠️ 注意：</strong> 如果您看不到右上角的綠色導出按鈕，請檢查腳本是否正確安裝和啟用。
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 功能測試</h2>
            
            <div class="button-group">
                <button class="test-button" onclick="openRawHtml()">
                    📄 打開原始 HTML 文件測試
                </button>
                <button class="test-button" onclick="testCurrentPage()">
                    🔍 測試當前頁面
                </button>
                <button class="test-button" onclick="showConsole()">
                    🛠️ 打開開發者工具
                </button>
            </div>
            
            <div id="testResults" style="margin-top: 20px;"></div>
        </div>

        <div class="test-section">
            <h2>📊 預期結果</h2>
            
            <div class="success">
                <strong>✅ 成功標準：</strong>
                <ul>
                    <li>頁面右上角出現綠色的 "📥 導出聯絡人為 CSV" 按鈕</li>
                    <li>點擊按鈕後能夠成功提取聯絡人資料</li>
                    <li>自動下載包含正確資料的 CSV 文件</li>
                    <li>顯示統計資訊窗口，包含聯絡人數量和部門資訊</li>
                    <li>CSV 文件包含正確的中文字符編碼</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🐛 故障排除</h2>
            
            <div class="step">
                <strong>問題 1：</strong> 看不到導出按鈕
                <br><em>解決方案：</em> 檢查 Tampermonkey 是否已啟用，腳本是否正確安裝
            </div>
            
            <div class="step">
                <strong>問題 2：</strong> 點擊按鈕沒有反應
                <br><em>解決方案：</em> 打開開發者工具查看控制台錯誤訊息
            </div>
            
            <div class="step">
                <strong>問題 3：</strong> CSV 文件中文亂碼
                <br><em>解決方案：</em> 使用支援 UTF-8 的軟件打開 CSV 文件
            </div>
            
            <div class="step">
                <strong>問題 4：</strong> 提取的資料不完整
                <br><em>解決方案：</em> 確保頁面完全載入，檢查網站結構是否有變化
            </div>
        </div>

        <div class="test-section">
            <h2>📈 測試報告</h2>
            
            <div id="testReport">
                <p>請執行測試後，在此記錄測試結果：</p>
                <textarea id="reportText" rows="10" cols="80" placeholder="請在此輸入測試結果和發現的問題..."></textarea>
                <br><br>
                <button class="test-button" onclick="saveReport()">💾 保存測試報告</button>
                <button class="test-button" onclick="clearReport()">🗑️ 清空報告</button>
            </div>
        </div>
    </div>

    <script>
        // 測試腳本功能
        function testCurrentPage() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h3>🔍 正在檢測...</h3>';
            
            setTimeout(() => {
                let html = '<h3>📋 檢測結果：</h3><ul>';
                
                // 檢查是否有導出按鈕
                const exportButton = document.querySelector('button[style*="position: fixed"]');
                if (exportButton) {
                    html += '<li>✅ 找到導出按鈕</li>';
                } else {
                    html += '<li>❌ 未找到導出按鈕</li>';
                }
                
                // 檢查 Tampermonkey
                if (typeof GM_info !== 'undefined') {
                    html += '<li>✅ Tampermonkey 已載入</li>';
                } else {
                    html += '<li>❌ Tampermonkey 未檢測到</li>';
                }
                
                // 檢查頁面標題
                if (document.title.includes('測試') || document.title.includes('Directory')) {
                    html += '<li>✅ 頁面標題符合預期</li>';
                } else {
                    html += '<li>⚠️ 頁面標題可能不符合腳本匹配規則</li>';
                }
                
                html += '</ul>';
                results.innerHTML = html;
            }, 1000);
        }
        
        function openRawHtml() {
            // 嘗試打開原始 HTML 文件
            const newWindow = window.open('../raw.html', '_blank');
            if (!newWindow) {
                alert('無法打開新窗口，請檢查瀏覽器彈出窗口設置。\n\n請手動打開 raw.html 文件進行測試。');
            }
        }
        
        function showConsole() {
            alert('請按 F12 或右鍵選擇 "檢查元素" 打開開發者工具，\n然後切換到 "Console" 標籤查看腳本運行狀態。');
        }
        
        function saveReport() {
            const reportText = document.getElementById('reportText').value;
            if (!reportText.trim()) {
                alert('請先輸入測試報告內容');
                return;
            }
            
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `測試報告_${timestamp}.txt`;
            
            const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            URL.revokeObjectURL(link.href);
            
            alert('測試報告已保存為：' + filename);
        }
        
        function clearReport() {
            if (confirm('確定要清空測試報告嗎？')) {
                document.getElementById('reportText').value = '';
            }
        }
        
        // 頁面載入時自動檢測
        window.addEventListener('load', function() {
            setTimeout(() => {
                testCurrentPage();
            }, 2000);
        });
        
        // 模擬聯絡人資料表格（用於測試）
        function createTestTable() {
            const testData = `
                <table border="0" cellpadding="1" cellspacing="2" width="100%">
                    <tr>
                        <td colspan="6" align="center">
                            <b><font size="+2">測試部門</font></b>
                        </td>
                    </tr>
                    <tr>
                        <th width="20%">Full Name</th>
                        <th width="20%">Post / Function</th>
                        <th width="20%">Telephone / Mobile / Pager</th>
                        <th width="10%">Fax</th>
                        <th width="10%">Email</th>
                        <th width="10%">Select All</th>
                    </tr>
                    <tr>
                        <td bgcolor="#FFFFFF">測試 用戶, Test</td>
                        <td bgcolor="#FFFFFF">測試工程師</td>
                        <td bgcolor="#FFFFFF">1234-5678</td>
                        <td bgcolor="#FFFFFF">1234-5679</td>
                        <td bgcolor="#FFFFFF"><a href="mailto:<EMAIL>"><EMAIL></a></td>
                        <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                    </tr>
                </table>
            `;
            
            const container = document.createElement('div');
            container.innerHTML = testData;
            container.style.margin = '20px 0';
            container.style.border = '1px solid #ccc';
            container.style.padding = '10px';
            container.style.backgroundColor = '#f9f9f9';
            
            const testSection = document.querySelector('.test-section:last-child');
            testSection.appendChild(container);
        }
        
        // 添加測試表格
        setTimeout(createTestTable, 1000);
    </script>
</body>
</html>
