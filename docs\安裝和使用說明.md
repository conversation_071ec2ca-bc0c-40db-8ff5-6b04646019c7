# 聯絡人目錄 CSV 導出器 - 安裝和使用說明

## 概述

這個 Tampermonkey 用戶腳本可以自動從電子電話目錄網站提取聯絡人資料，並將其導出為 CSV 格式文件，方便進一步處理和管理。

## 功能特點

- ✅ **自動檢測**：自動識別聯絡人目錄頁面
- ✅ **完整提取**：提取姓名、職位、電話、傳真、電郵等完整資訊
- ✅ **部門分類**：自動識別並記錄部門資訊
- ✅ **CSV 導出**：生成標準 CSV 格式，支援中文字符
- ✅ **用戶友好**：提供直觀的操作介面和結果統計
- ✅ **錯誤處理**：包含完善的錯誤處理機制

## 安裝步驟

### 1. 安裝 Tampermonkey 瀏覽器擴展

#### Chrome 瀏覽器：
1. 打開 [Chrome Web Store](https://chrome.google.com/webstore/category/extensions)
2. 搜索 "Tampermonkey"
3. 點擊 "添加至 Chrome" 安裝

#### Firefox 瀏覽器：
1. 打開 [Firefox Add-ons](https://addons.mozilla.org/)
2. 搜索 "Tampermonkey"
3. 點擊 "添加至 Firefox" 安裝

#### Edge 瀏覽器：
1. 打開 [Microsoft Edge Add-ons](https://microsoftedge.microsoft.com/addons/)
2. 搜索 "Tampermonkey"
3. 點擊 "獲取" 安裝

### 2. 安裝用戶腳本

1. **打開 Tampermonkey 管理面板**
   - 點擊瀏覽器工具欄中的 Tampermonkey 圖標
   - 選擇 "管理面板"

2. **創建新腳本**
   - 點擊 "+" 按鈕或 "創建新腳本"
   - 刪除默認內容

3. **複製腳本代碼**
   - 打開 `tampermonkey-contact-extractor.js` 文件
   - 複製全部內容
   - 粘貼到 Tampermonkey 編輯器中

4. **保存腳本**
   - 按 `Ctrl+S` (Windows/Linux) 或 `Cmd+S` (Mac) 保存
   - 或點擊 "文件" → "保存"

## 使用方法

### 1. 訪問聯絡人目錄頁面

腳本會在以下類型的頁面自動激活：
- `*://intra.abc.edu.hk/directory/*`
- `*://*/directory/*`
- `file://*raw.html*`

### 2. 使用導出功能

1. **查看導出按鈕**
   - 頁面載入後，右上角會出現綠色的 "📥 導出聯絡人為 CSV" 按鈕

2. **執行導出**
   - 點擊導出按鈕
   - 腳本會自動掃描頁面並提取所有聯絡人資料

3. **下載 CSV 文件**
   - 提取完成後會自動下載 CSV 文件
   - 文件名格式：`聯絡人目錄_YYYY-MM-DD-HH-mm-ss.csv`

4. **查看統計資訊**
   - 導出成功後會顯示統計資訊窗口
   - 包含總聯絡人數、部門數量等資訊

## CSV 文件格式

導出的 CSV 文件包含以下欄位：

| 欄位名稱 | 說明 | 示例 |
|---------|------|------|
| 部門 | 聯絡人所屬部門 | Human Resources Office |
| 姓名 | 聯絡人全名（原始格式） | Mr WOO WOO, Kenny |
| 英文姓名 | 重新格式化的英文姓名 | Kenny Woo |
| 職位/職能 | 職位或職能描述 | Director |
| 電話 | 電話/手機/傳呼機號碼 | 0000-0000 |
| 傳真 | 傳真號碼 | 0000-0000 |
| 電郵 | 電子郵件地址 | <EMAIL> |

### 英文姓名轉換規則

腳本會自動將原始姓名格式轉換為標準的英文姓名格式：

| 原始格式 | 轉換後格式 | 說明 |
|---------|-----------|------|
| Mr WOO WOO, Kenny | Kenny Woo | 移除稱謂，提取名字和第一個姓氏 |
| Ms LEE LEE, Jackie | Jackie Lee | 標準轉換：名字 + 姓氏 |
| Ms CHOW Winnie | Winnie Chow | 無逗號格式：最後一個詞為名字 |
| Miss CHAN Lok Lam, Laura | Laura Chan | 支援 Miss 稱謂 |
| Mr CHAU CHAU CHAU | CHAU CHAU CHAU | 三個相同姓氏保持原格式 |

## 故障排除

### 常見問題

**Q: 點擊導出按鈕後沒有反應？**
A: 
- 檢查瀏覽器是否允許下載文件
- 確保頁面完全載入
- 查看瀏覽器控制台是否有錯誤訊息

**Q: 導出的 CSV 文件中文顯示亂碼？**
A: 
- 使用 Excel 打開時選擇 UTF-8 編碼
- 或使用 Google Sheets、LibreOffice Calc 等支援 UTF-8 的軟件

**Q: 腳本沒有在目標頁面激活？**
A: 
- 檢查 URL 是否符合腳本的匹配規則
- 確保 Tampermonkey 已啟用
- 檢查腳本是否已正確安裝和啟用

**Q: 提取的聯絡人數量不正確？**
A: 
- 確保頁面完全載入所有內容
- 檢查是否有動態載入的內容
- 查看瀏覽器控制台的錯誤訊息

### 調試方法

1. **開啟瀏覽器開發者工具**
   - 按 F12 或右鍵選擇 "檢查元素"

2. **查看控制台訊息**
   - 切換到 "Console" 標籤
   - 查看是否有錯誤或警告訊息

3. **檢查腳本狀態**
   - 點擊 Tampermonkey 圖標
   - 確認腳本顯示為已啟用狀態

## 技術細節

### 支援的網站結構

腳本能夠識別以下 HTML 結構：
- 標準表格格式的聯絡人列表
- 包含部門標題的分組結構
- 電郵地址的 `mailto:` 連結

### 資料提取邏輯

1. **部門識別**：通過 `colspan="6"` 的表格單元格識別部門標題
2. **表頭檢測**：尋找包含 "Full Name"、"Post"、"Email" 的表頭行
3. **資料提取**：從表格行中提取對應欄位的文本內容
4. **電郵處理**：優先提取 `mailto:` 連結中的電郵地址

### 安全性考慮

- 腳本僅在指定的網站域名下運行
- 不會向外部服務器發送任何資料
- 所有處理都在本地瀏覽器中完成

## 更新和維護

### 版本更新

當有新版本可用時：
1. 在 Tampermonkey 管理面板中找到該腳本
2. 點擊腳本名稱進入編輯模式
3. 替換為新版本的代碼
4. 保存更改

### 自定義修改

如需修改腳本以適應不同的網站結構：
1. 修改 `@match` 規則以匹配目標網站
2. 調整 `extractContacts()` 函數中的選擇器
3. 根據需要修改 CSV 欄位結構

## 支援和反饋

如果您在使用過程中遇到問題或有改進建議，請：
1. 檢查本說明文件的故障排除部分
2. 查看瀏覽器控制台的錯誤訊息
3. 記錄具體的錯誤情況和步驟

## 許可證

本腳本僅供學習和個人使用，請遵守相關網站的使用條款和隱私政策。
