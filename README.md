# 聯絡人目錄 CSV 導出器

一個專為電子電話目錄網站設計的 Tampermonkey 用戶腳本，能夠自動提取聯絡人資料並導出為 CSV 格式。

## 🌟 功能特點

- **🔍 智能識別**：自動檢測聯絡人目錄頁面結構
- **📊 完整提取**：提取姓名、職位、電話、傳真、電郵等完整資訊
- **🏢 精確部門分類**：正確識別組織單位（Office/Section），避免錯誤分類
- **🚫 智能去重**：自動去除重複聯絡人，確保每人只出現一次
- **📁 CSV 導出**：生成標準 CSV 格式，完美支援中文字符
- **🎯 用戶友好**：一鍵操作，直觀的操作介面
- **📈 統計資訊**：顯示提取結果統計和去重日誌
- **🛡️ 安全可靠**：本地處理，不上傳任何資料
- **🔧 調試友好**：詳細的控制台日誌，便於問題排查

## 📁 項目結構

```
telephone directory_v2/
├── raw.html                           # 原始聯絡人目錄 HTML 文件
├── README.md                          # 項目說明文件
├── docs/                              # 文檔目錄
│   ├── tampermonkey-contact-extractor.js  # Tampermonkey 用戶腳本 (v1.2)
│   ├── 安裝和使用說明.md                    # 詳細安裝和使用指南
│   ├── 快速安裝指南.md                      # 5分鐘快速設置指南
│   ├── 修復說明.md                          # 問題修復和改進說明
│   └── 英文姓名功能說明.md                  # 英文姓名轉換功能詳解
└── test/                              # 測試目錄
    ├── test-extractor.html            # 測試頁面
    ├── demo-script.js                 # 演示腳本
    ├── validation-script.js           # 驗證腳本
    └── english-name-test.js           # 英文姓名功能測試
```

## 🚀 快速開始

### 1. 安裝 Tampermonkey

首先在您的瀏覽器中安裝 Tampermonkey 擴展：

- **Chrome**: [Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Firefox Add-ons](https://addons.mozilla.org/firefox/addon/tampermonkey/)
- **Edge**: [Microsoft Edge Add-ons](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

### 2. 安裝腳本

1. 打開 Tampermonkey 管理面板
2. 點擊 "創建新腳本"
3. 複製 `docs/tampermonkey-contact-extractor.js` 中的代碼
4. 粘貼到編輯器中並保存

### 3. 使用腳本

1. 打開聯絡人目錄頁面（如 `raw.html`）
2. 點擊右上角出現的綠色 "📥 導出聯絡人為 CSV" 按鈕
3. 等待處理完成，CSV 文件將自動下載

## 📋 支援的資料格式

腳本能夠提取以下聯絡人資訊：

| 欄位 | 說明 | 示例 |
|------|------|------|
| 部門 | 聯絡人所屬部門 | Human Resources Office |
| 姓名 | 聯絡人全名（原始格式） | Mr WOO WOO, Kenny |
| 英文姓名 | 重新格式化的英文姓名 | Kenny Woo |
| 職位/職能 | 職位或職能描述 | Director |
| 電話 | 電話/手機/傳呼機號碼 | 0000-0000 |
| 傳真 | 傳真號碼 | 0000-0000 |
| 電郵 | 電子郵件地址 | <EMAIL> |

### 🔄 英文姓名自動轉換

腳本包含智能的英文姓名轉換功能：
- **Mr WOO WOO, Kenny** → **Kenny Woo**
- **Ms LEE LEE, Jackie** → **Jackie Lee**
- **Ms CHOW Winnie** → **Winnie Chow**
- **Miss CHAN Lok Lam, Laura** → **Laura Chan**

## 🎯 適用網站

腳本設計用於以下類型的網站：

- `*://intra.abc.edu.hk/directory/*`
- `*://*/directory/*`
- `file://*raw.html*`
- 任何包含類似結構的聯絡人目錄頁面

## 🧪 測試

項目包含完整的測試環境：

1. 打開 `test/test-extractor.html` 進行功能測試
2. 按照測試頁面的指引驗證腳本功能
3. 檢查導出的 CSV 文件格式和內容

## 📖 詳細文檔

- **[安裝和使用說明](docs/安裝和使用說明.md)** - 完整的安裝、使用和故障排除指南
- **[腳本源碼](docs/tampermonkey-contact-extractor.js)** - 帶註釋的完整腳本代碼

## 🔧 技術細節

### 核心功能

- **頁面檢測**：智能識別聯絡人目錄頁面
- **資料提取**：解析 HTML 表格結構，提取聯絡人資訊
- **格式轉換**：將提取的資料轉換為標準 CSV 格式
- **文件下載**：生成並自動下載 CSV 文件

### 技術特點

- 純 JavaScript 實現，無外部依賴
- 支援 UTF-8 編碼，完美處理中文字符
- 智能錯誤處理和用戶反饋
- 響應式設計，適配不同螢幕尺寸
- **精確的部門識別**：使用嚴格的 HTML 結構匹配
- **高效去重算法**：基於 Map 的 O(1) 查找性能
- **詳細調試日誌**：便於問題診斷和功能驗證

## 🛠️ 自定義配置

### 修改匹配規則

如需適配其他網站，可修改腳本頭部的 `@match` 規則：

```javascript
// @match        *://your-domain.com/directory/*
```

### 調整提取邏輯

可根據不同的 HTML 結構修改 `extractContacts()` 函數中的選擇器。

## 🐛 故障排除

### 常見問題

1. **看不到導出按鈕**
   - 檢查 Tampermonkey 是否已啟用
   - 確認腳本已正確安裝
   - 驗證頁面 URL 是否匹配腳本規則

2. **導出的 CSV 文件中文亂碼**
   - 使用支援 UTF-8 的軟件打開（如 Google Sheets）
   - 在 Excel 中選擇正確的編碼格式

3. **提取的資料不完整**
   - 確保頁面完全載入
   - 檢查網站結構是否有變化
   - 查看瀏覽器控制台的錯誤訊息

### 調試方法

1. 按 F12 打開開發者工具
2. 切換到 Console 標籤
3. 查看腳本運行狀態和錯誤訊息

## 📄 許可證

本項目僅供學習和個人使用。使用時請遵守相關網站的使用條款和隱私政策。

## 🤝 貢獻

歡迎提交問題報告和改進建議！

## 📞 支援

如果您在使用過程中遇到問題：

1. 查看 [安裝和使用說明](docs/安裝和使用說明.md) 中的故障排除部分
2. 檢查瀏覽器控制台的錯誤訊息
3. 使用測試頁面驗證腳本功能

---

**注意**：本腳本設計用於合法的資料整理用途，請確保您有權限訪問和使用相關聯絡人資料。
