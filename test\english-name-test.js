// 英文姓名提取功能測試腳本
// 在瀏覽器控制台中運行此腳本來測試英文姓名轉換功能

(function() {
    'use strict';
    
    console.log('🧪 開始測試英文姓名提取功能...');
    
    // 提取英文姓名函數（與主腳本相同）
    function extractEnglishName(originalName) {
        if (!originalName || typeof originalName !== 'string') {
            return '';
        }
        
        const name = originalName.trim();
        
        // 定義稱謂列表
        const titles = ['Mr', 'Ms', 'Miss', 'Mrs', 'Dr', 'Prof'];
        
        // 移除稱謂
        let nameWithoutTitle = name;
        for (const title of titles) {
            if (name.startsWith(title + ' ')) {
                nameWithoutTitle = name.substring(title.length + 1).trim();
                break;
            }
        }
        
        // 檢查是否有逗號分隔
        if (nameWithoutTitle.includes(',')) {
            // 格式：「姓氏 姓氏, 名字」或「姓氏, 名字」
            const parts = nameWithoutTitle.split(',');
            if (parts.length >= 2) {
                const surnamesPart = parts[0].trim(); // 姓氏部分
                const givenName = parts[1].trim();    // 名字部分
                
                // 取姓氏的第一個單詞作為英文姓氏
                const surnames = surnamesPart.split(/\s+/);
                const englishSurname = surnames[0];
                
                // 組合：名字 + 姓氏
                if (givenName && englishSurname) {
                    return `${givenName} ${englishSurname}`;
                }
            }
        } else {
            // 沒有逗號的情況，可能是「姓氏 名字」格式
            const parts = nameWithoutTitle.split(/\s+/);
            if (parts.length >= 2) {
                // 假設最後一個是名字，前面的是姓氏
                const givenName = parts[parts.length - 1];
                const englishSurname = parts[0];
                
                // 檢查是否是重複姓氏的情況（如 "WONG WONG"）
                if (parts.length >= 2 && parts[0] === parts[1]) {
                    // 重複姓氏情況，保持原格式
                    return nameWithoutTitle;
                } else if (parts.length === 2) {
                    // 標準「姓氏 名字」格式
                    return `${givenName} ${englishSurname}`;
                }
            }
        }
        
        // 如果無法解析，返回去除稱謂後的原始姓名
        return nameWithoutTitle || originalName;
    }
    
    // 測試案例
    const testCases = [
        // 標準格式：稱謂 + 重複姓氏 + 逗號 + 名字
        { input: 'Mr WOO WOO, Kenny', expected: 'Kenny Woo' },
        { input: 'Ms LEE LEE, Jackie', expected: 'Jackie Lee' },
        { input: 'Ms TUNG TUNG, Dora', expected: 'Dora Tung' },
        { input: 'Ms CHAO CHAO, Iris', expected: 'Iris Chao' },
        { input: 'Ms YUNG YUNG, Polly', expected: 'Polly Yung' },
        { input: 'Mr CHAN CHAN, Jacky', expected: 'Jacky Chan' },
        { input: 'Ms TANG TANG, Grace', expected: 'Grace Tang' },
        { input: 'Mr WONG WONG, James', expected: 'James Wong' },
        { input: 'Ms SHIU SHIU, Crystal', expected: 'Crystal Shiu' },
        { input: 'Ms CHAN CHAN, Kelly', expected: 'Kelly Chan' },
        
        // 無重複姓氏格式
        { input: 'Ms CHOW Winnie', expected: 'Winnie Chow' },
        { input: 'Mr KUNG Kam Yau', expected: 'Yau Kung' },
        { input: 'Mr WONG Ryan Shek Ming', expected: 'Ming Wong' },
        
        // Miss 稱謂
        { input: 'Miss CHAN Lok Lam, Laura', expected: 'Laura Chan' },
        
        // 三個相同姓氏（無逗號）
        { input: 'Mr CHAU CHAU CHAU', expected: 'CHAU CHAU CHAU' },
        { input: 'Ms WONG WONG WONG', expected: 'WONG WONG WONG' },
        { input: 'Mr LAW LAW LAW', expected: 'LAW LAW LAW' },
        { input: 'Ms HO HO HO', expected: 'HO HO HO' },
        
        // 單一姓氏格式
        { input: 'Ms LEUNG LEUNG, Carmen', expected: 'Carmen Leung' },
        { input: 'Mr WONG WONG, Justin', expected: 'Justin Wong' },
        { input: 'Ms TSANG TSANG, Jaime', expected: 'Jaime Tsang' },
        
        // 邊界情況
        { input: '', expected: '' },
        { input: 'Mr', expected: '' },
        { input: 'Ms CHAN', expected: 'CHAN' },
        { input: 'WONG WONG, Kenny', expected: 'Kenny Wong' }, // 無稱謂
        
        // 複雜格式
        { input: 'Mr IP IP, Dick', expected: 'Dick Ip' },
        { input: 'Ms LI LI, Mable', expected: 'Mable Li' },
        { input: 'Mr LAM LAM, Ivan', expected: 'Ivan Lam' },
        { input: 'Ms LIN LIN, Michelle', expected: 'Michelle Lin' },
        { input: 'Mr TSOI TSOI, Leon', expected: 'Leon Tsoi' }
    ];
    
    // 執行測試
    function runTests() {
        console.log('\n📋 開始執行測試案例...\n');
        
        let passedTests = 0;
        let failedTests = 0;
        const failures = [];
        
        testCases.forEach((testCase, index) => {
            const result = extractEnglishName(testCase.input);
            const passed = result === testCase.expected;
            
            if (passed) {
                console.log(`✅ 測試 ${index + 1}: "${testCase.input}" → "${result}"`);
                passedTests++;
            } else {
                console.log(`❌ 測試 ${index + 1}: "${testCase.input}" → "${result}" (預期: "${testCase.expected}")`);
                failedTests++;
                failures.push({
                    index: index + 1,
                    input: testCase.input,
                    expected: testCase.expected,
                    actual: result
                });
            }
        });
        
        console.log(`\n📊 測試結果統計:`);
        console.log(`  ✅ 通過: ${passedTests} 個`);
        console.log(`  ❌ 失敗: ${failedTests} 個`);
        console.log(`  📈 成功率: ${((passedTests / testCases.length) * 100).toFixed(1)}%`);
        
        if (failures.length > 0) {
            console.log(`\n🔍 失敗案例詳情:`);
            failures.forEach(failure => {
                console.log(`  ${failure.index}. 輸入: "${failure.input}"`);
                console.log(`     預期: "${failure.expected}"`);
                console.log(`     實際: "${failure.actual}"`);
                console.log('');
            });
        }
        
        return { passed: passedTests, failed: failedTests, failures };
    }
    
    // 從實際頁面提取姓名進行測試
    function testRealPageNames() {
        console.log('\n🌐 測試實際頁面中的姓名...\n');
        
        const nameElements = document.querySelectorAll('td[bgcolor="#FFFFFF"]');
        const realNames = [];
        
        nameElements.forEach(el => {
            const text = el.textContent.trim();
            // 檢查是否是姓名格式（包含稱謂）
            if (text.match(/^(Mr|Ms|Miss|Mrs|Dr|Prof)\s+/)) {
                realNames.push(text);
            }
        });
        
        // 去重並取前20個進行測試
        const uniqueNames = [...new Set(realNames)].slice(0, 20);
        
        console.log(`找到 ${uniqueNames.length} 個真實姓名進行測試:`);
        
        uniqueNames.forEach((name, index) => {
            const englishName = extractEnglishName(name);
            console.log(`${index + 1}. "${name}" → "${englishName}"`);
        });
        
        return uniqueNames;
    }
    
    // 生成測試報告
    function generateTestReport(testResults, realNames) {
        const report = {
            timestamp: new Date().toISOString(),
            testCases: {
                total: testCases.length,
                passed: testResults.passed,
                failed: testResults.failed,
                successRate: ((testResults.passed / testCases.length) * 100).toFixed(1) + '%'
            },
            failures: testResults.failures,
            realPageTest: {
                namesFound: realNames.length,
                samples: realNames.slice(0, 10).map(name => ({
                    original: name,
                    converted: extractEnglishName(name)
                }))
            }
        };
        
        // 創建下載連結
        const reportJson = JSON.stringify(report, null, 2);
        const blob = new Blob([reportJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `英文姓名測試報告_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        link.textContent = '📥 下載測試報告 (JSON)';
        link.style.cssText = `
            display: inline-block;
            background: #FF9800;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            font-weight: bold;
        `;
        
        // 添加到頁面
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background: white;
            border: 2px solid #FF9800;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            max-width: 400px;
        `;
        
        container.innerHTML = `
            <h3 style="margin-top: 0; color: #FF9800;">🧪 英文姓名測試完成</h3>
            <p><strong>測試案例:</strong> ${report.testCases.total} 個</p>
            <p><strong>成功率:</strong> ${report.testCases.successRate}</p>
            <p><strong>通過:</strong> ${report.testCases.passed} 個</p>
            <p><strong>失敗:</strong> ${report.testCases.failed} 個</p>
            <p><strong>實際姓名:</strong> ${report.realPageTest.namesFound} 個</p>
            <button onclick="this.parentElement.remove()" 
                    style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; float: right;">
                關閉
            </button>
        `;
        
        container.appendChild(link);
        document.body.appendChild(container);
        
        return report;
    }
    
    // 執行完整測試
    function runCompleteTest() {
        try {
            const testResults = runTests();
            const realNames = testRealPageNames();
            const report = generateTestReport(testResults, realNames);
            
            console.log('\n🎉 英文姓名提取功能測試完成！查看頁面右上角的結果窗口。');
            return report;
            
        } catch (error) {
            console.error('❌ 測試過程中發生錯誤:', error);
        }
    }
    
    // 延遲執行測試
    setTimeout(runCompleteTest, 1000);
    
})();

console.log(`
🧪 英文姓名提取功能測試腳本

此腳本將測試以下轉換規則:
1. ✅ Mr WOO WOO, Kenny → Kenny Woo
2. ✅ Ms LEE LEE, Jackie → Jackie Lee  
3. ✅ Ms CHOW Winnie → Winnie Chow
4. ✅ Miss CHAN Lok Lam, Laura → Laura Chan
5. ✅ Mr CHAU CHAU CHAU → CHAU CHAU CHAU (保持原格式)

腳本將在1秒後自動開始測試...
`);
