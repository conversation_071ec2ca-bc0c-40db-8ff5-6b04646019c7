<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Electronic Telephone Directory</title>
    <link rel="stylesheet" href="./lib/css/itsd.css">
    <script language="javascript" src="./lib/js/sbdResult.js"></script>
    <style data-id="immersive-translate-input-injected-css">
        .immersive-translate-input {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            bottom: 0;
            z-index: 2147483647;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .immersive-translate-attach-loading::after {
            content: " ";

            --loading-color: #f78fb6;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            display: block;
            margin: 12px auto;
            position: relative;
            color: white;
            left: -100px;
            box-sizing: border-box;
            animation: immersiveTranslateShadowRolling 1.5s linear infinite;

            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-2000%, -50%);
            z-index: 100;
        }

        .immersive-translate-loading-spinner {
            vertical-align: middle !important;
            width: 10px !important;
            height: 10px !important;
            display: inline-block !important;
            margin: 0 4px !important;
            border: 2px rgba(221, 244, 255, 0.6) solid !important;
            border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
            border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
            border-radius: 50% !important;
            padding: 0 !important;
            -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
            animation: immersive-translate-loading-animation 0.6s infinite linear !important;
        }

        @-webkit-keyframes immersive-translate-loading-animation {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(359deg);
            }
        }

        @keyframes immersive-translate-loading-animation {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(359deg);
            }
        }

        .immersive-translate-input-loading {
            --loading-color: #f78fb6;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            display: block;
            margin: 12px auto;
            position: relative;
            color: white;
            left: -100px;
            box-sizing: border-box;
            animation: immersiveTranslateShadowRolling 1.5s linear infinite;
        }

        @keyframes immersiveTranslateShadowRolling {
            0% {
                box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
                    0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            12% {
                box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
                    0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            25% {
                box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
                    0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
            }

            36% {
                box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
                    100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
            }

            50% {
                box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
                    110px 0 var(--loading-color), 100px 0 var(--loading-color);
            }

            62% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
                    120px 0 var(--loading-color), 110px 0 var(--loading-color);
            }

            75% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
                    130px 0 var(--loading-color), 120px 0 var(--loading-color);
            }

            87% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
                    200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
            }

            100% {
                box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
                    200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
            }
        }

        .immersive-translate-toast {
            display: flex;
            position: fixed;
            z-index: 2147483647;
            left: 0;
            right: 0;
            top: 1%;
            width: fit-content;
            padding: 12px 20px;
            margin: auto;
            overflow: auto;
            background: #fef6f9;
            box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
            font-size: 15px;
            border-radius: 8px;
            color: #333;
        }

        .immersive-translate-toast-content {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .immersive-translate-toast-hidden {
            margin: 0 20px 0 72px;
            text-decoration: underline;
            cursor: pointer;
        }

        .immersive-translate-toast-close {
            color: #666666;
            font-size: 20px;
            font-weight: bold;
            padding: 0 10px;
            cursor: pointer;
        }

        @media screen and (max-width: 768px) {
            .immersive-translate-toast {
                top: 0;
                padding: 12px 0px 0 10px;
            }

            .immersive-translate-toast-content {
                flex-direction: column;
                text-align: center;
            }

            .immersive-translate-toast-hidden {
                margin: 10px auto;
            }
        }

        .immersive-translate-modal {
            display: none;
            position: fixed;
            z-index: 2147483647;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgb(0, 0, 0);
            background-color: rgba(0, 0, 0, 0.4);
            font-size: 15px;
        }

        .immersive-translate-modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 40px 24px 24px;
            border-radius: 12px;
            width: 350px;
            font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
                "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
                "Segoe UI Symbol", "Noto Color Emoji";
            position: relative;
        }

        @media screen and (max-width: 768px) {
            .immersive-translate-modal-content {
                margin: 50% auto !important;
            }
        }

        .immersive-translate-modal .immersive-translate-modal-content-in-input {
            max-width: 500px;
        }

        .immersive-translate-modal-content-in-input .immersive-translate-modal-body {
            text-align: left;
            max-height: unset;
        }

        .immersive-translate-modal-title {
            text-align: center;
            font-size: 16px;
            font-weight: 700;
            color: #333333;
        }

        .immersive-translate-modal-body {
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            margin-top: 24px;
        }

        @media screen and (max-width: 768px) {
            .immersive-translate-modal-body {
                max-height: 250px;
                overflow-y: auto;
            }
        }

        .immersive-translate-close {
            color: #666666;
            position: absolute;
            right: 16px;
            top: 16px;
            font-size: 20px;
            font-weight: bold;
        }

        .immersive-translate-close:hover,
        .immersive-translate-close:focus {
            text-decoration: none;
            cursor: pointer;
        }

        .immersive-translate-modal-footer {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 24px;
        }

        .immersive-translate-btn {
            width: fit-content;
            color: #fff;
            background-color: #ea4c89;
            border: none;
            font-size: 14px;
            margin: 0 8px;
            padding: 9px 30px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .immersive-translate-btn:hover {
            background-color: #f082ac;
        }

        .immersive-translate-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .immersive-translate-btn:disabled:hover {
            background-color: #ea4c89;
        }

        .immersive-translate-cancel-btn {
            /* gray color */
            background-color: rgb(89, 107, 120);
        }

        .immersive-translate-cancel-btn:hover {
            background-color: hsl(205, 20%, 32%);
        }

        .immersive-translate-action-btn {
            background-color: transparent;
            color: #ea4c89;
            border: 1px solid #ea4c89;
        }

        .immersive-translate-btn svg {
            margin-right: 5px;
        }

        .immersive-translate-link {
            cursor: pointer;
            user-select: none;
            -webkit-user-drag: none;
            text-decoration: none;
            color: #007bff;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        }

        .immersive-translate-primary-link {
            cursor: pointer;
            user-select: none;
            -webkit-user-drag: none;
            text-decoration: none;
            color: #ea4c89;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        }

        .immersive-translate-modal input[type="radio"] {
            margin: 0 6px;
            cursor: pointer;
        }

        .immersive-translate-modal label {
            cursor: pointer;
        }

        .immersive-translate-close-action {
            position: absolute;
            top: 2px;
            right: 0px;
            cursor: pointer;
        }

        .imt-image-status {
            background-color: rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 16px !important;
        }

        .imt-image-status img,
        .imt-image-status svg,
        .imt-img-loading {
            width: 28px !important;
            height: 28px !important;
            margin: 0 0 8px 0 !important;
            min-height: 28px !important;
            min-width: 28px !important;
            position: relative !important;
        }

        .imt-img-loading {
            background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
            background-size: 28px 28px;
            animation: image-loading-rotate 1s linear infinite !important;
        }

        .imt-image-status span {
            color: var(--bg-2, #fff) !important;
            font-size: 14px !important;
            line-height: 14px !important;
            font-weight: 500 !important;
            font-family: "PingFang SC", Arial, sans-serif !important;
        }

        @keyframes image-loading-rotate {
            from {
                transform: rotate(360deg);
            }

            to {
                transform: rotate(0deg);
            }
        }
    </style>
    <style class="automa-element-selector">
        @font-face {
            font-family: "Inter var";
            font-weight: 100 900;
            font-display: swap;
            font-style: normal;
            font-named-instance: "Regular";
            src: url("chrome-extension://infppggnoaenmfagbfknfkancpbljcca/Inter-roman-latin.var.woff2") format("woff2")
        }

        .automa-element-selector {
            direction: ltr
        }

        [automa-isDragging] {
            user-select: none
        }

        [automa-el-list] {
            outline: 2px dashed #6366f1;
        }
    </style>
</head>

<body>
    <table cellpadding="0" cellspacing="0" border="0" width="100%">
        <tbody>
            <tr>
                <td align="left"><img src="./img/etdlogo.gif"></td>
                <td>
                </td>
                <td align="right"><a
                        href="https://intra.abc.edu.hk/sso/endpoint/logout?ReturnTo=https://login.abc.edu.hk/adfs/ls/?wa=wsignout1.0">Logout</a>&nbsp;|&nbsp;<a
                        href="./sbdResult.asp?lang=chi&amp;site=VTCTO&amp;dept=HRD">中文</a>&nbsp;|&nbsp;<font size="-2">
                        <a href="./etd_terms_of_use.html" target="terms">Terms of Use</a></font>
                </td>
            </tr>
        </tbody>
    </table>
    <div align="RIGHT">
        <font size="2" color="#0099FF">Data contained in this website is provided/updated by respective operational
            units.</font>
    </div>
    <br>

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tbody>
            <tr>
                <td>
                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tbody>
                            <tr>
                                <td bgcolor="#3388FF">&nbsp;</td>
                            </tr><!-- blank line -->
                            <tr>
                                <td valign="bottom" bgcolor="#3388FF" align="left">

                                    <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <a
                                                        href="https://intra.abc.edu.hk/directory/prod/sbnIndex.asp?lang=eng"><img
                                                            src="./img/e_searchByName2.gif" border="0"
                                                            alt="Search By Name"></a><a
                                                        href="https://intra.abc.edu.hk/directory/prod/sbdIndex.asp?lang=eng"><img
                                                            src="./img/e_searchByDept1.gif" border="0"
                                                            alt="Search By Department"></a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td align="center" bgcolor="#e6ebef">
                    <table border="0" cellpadding="1" cellspacing="2" width="100%">

                        <tbody>
                            <tr>
                                <td colspan="6" align="center">
                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+2">Human Resources Office</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5"><b>Office
                                                        Address</b>&nbsp;&nbsp;</td>
                                                <td>Hong Kong</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Fax</b>&nbsp;&nbsp;</td>
                                                <td>0000-0000</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email</b>&nbsp;&nbsp;</td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td align="right" width="50%" colspan="5" valign="top"><b>Opening
                                                        Hours</b>&nbsp;&nbsp;</td>
                                                <td valign="top">08:30 - 17:15 (Mon - Fri)<br>Closed (Sat, Sun &amp;
                                                    PH)<br></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                    <!-- ITO Helpdesk start -->



                                    <!-- ITSD Helpdesk end -->

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr WOO WOO, Kenny</td>
                                <td bgcolor="#FFFFFF">Director</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TUNG TUNG, Dora</td>
                                <td bgcolor="#FFFFFF">Deputy Director (1)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Mabel</td>
                                <td bgcolor="#FFFFFF">Deputy Director (2)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAO CHAO, Iris</td>
                                <td bgcolor="#FFFFFF">Deputy Director (3)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Sandy</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Digitalization and Administration)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 5)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Employee Benefits Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Fax&nbsp;&nbsp;</b></td>
                                                <td>0000-0000</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHENG CHENG, Judith</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Employee Benefits)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHOW Winnie</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Employee Benefits) </td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LEE LEE, Jackie</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Employee Benefits) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms YUNG YUNG, Polly</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Employee Benefits) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG, Jessica</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Employee Benefits) 3</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 5)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6"><i><b>
                                                            <font>Leave, Leave Passage &amp; MPF Scheme</font>
                                                        </b></i></td>
                                            </tr>

                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG, Bonny</td>
                                <td bgcolor="#FFFFFF">Clerical Officer I (Employee Benefits) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Samly</td>
                                <td bgcolor="#FFFFFF">MPF Scheme, Leave &amp; Leave Passage</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms SUEN SUEN, Esther</td>
                                <td bgcolor="#FFFFFF">MPF Scheme</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 3)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6"><i><b>
                                                            <font>Medical Benefits, Education Allowances &amp; School
                                                                Passage</font>
                                                        </b></i></td>
                                            </tr>

                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr CHAN CHAN, Jacky</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Employee Benefits) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Rebecca</td>
                                <td bgcolor="#FFFFFF">NRP Medical Benefits &amp; LEA</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LAI LAI, Mandy</td>
                                <td bgcolor="#FFFFFF">ORP Medical Benefits, OEA &amp; School Passage</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LAI LAI, Yoki</td>
                                <td bgcolor="#FFFFFF">ORP Medical Benefits</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 4)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6"><i><b>
                                                            <font>Housing Benefits, Provident Fund Scheme, Staff Loan
                                                                &amp; Employees' Compensation</font>
                                                        </b></i></td>
                                            </tr>

                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TANG TANG, Grace</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Employee Benefits) 3</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LI LI, Mable</td>
                                <td bgcolor="#FFFFFF">PTA, SSQ &amp; PFS99</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr WONG WONG, James</td>
                                <td bgcolor="#FFFFFF">PTA &amp; Death Benefit Scheme</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 3)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">
                                    &nbsp;
                                </td>
                            </tr>

                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Staff Development Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms SHIU SHIU, Crystal</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Staff Development &amp; Staff Relations)
                                </td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Kelly</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Staff Development)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHENG CHENG, Vivian</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Staff Development) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms PUN PUN, Macy</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Staff Development) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Winnie</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms PUN PUN, Albee</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG, Vickie</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 7)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Staff Relations Section (including Staff
                                                            Relations, Staff Complaints and Discipline)</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms SHIU SHIU, Crystal</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Staff Development &amp; Staff Relations)
                                </td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms MAN MAN, Yan</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Staff Relations)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LUK LUK, Majanda</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Staff Relations)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr CHAU CHAU CHAU</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Staff Relations)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 4)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Performance Management Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG WONG </td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Performance Management &amp; Pay
                                    Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WAN WAN, Eki</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Performance Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Idy</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Performance Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN CHAN</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr KWONG KWONG, Wilfred</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 5)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Pay Management Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG WONG</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Performance Management &amp; Pay
                                    Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr LAW LAW LAW</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Pay Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms HO HO HO</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Pay Management)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Anke</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 4)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Manpower Planning Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Fax&nbsp;&nbsp;</b></td>
                                                <td>0000-0000</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LUI LUI, Iris</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Manpower Planning)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr WONG WONG, Justin</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Manpower Planning)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LEUNG LEUNG, Carmen</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Manpower Planning)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms SAM SAM, Christine</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 4)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Appointments Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Fax&nbsp;&nbsp;</b></td>
                                                <td>0000-0000</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>

                            <tr>
                                <td colspan="6">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6"><i><b>
                                                            <font>Team 1</font>
                                                        </b></i></td>
                                            </tr>

                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TSANG TSANG, Jaime</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Appointments) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Dorcas</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Appointments) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LEE LEE, Piko</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHEUNG CHEUNG, Christie</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TANG TANG, Lyna</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 3</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr CHAN CHAN, Michael</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 4</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr IP IP, Dick</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Appointments) 1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAM CHAM, Dorothy</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Elaine</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHEN CHEN, Michelle</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHEUK CHEUK, Cathy</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHEUNG CHEUNG, Evelyn</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms HAU HAU, Jody</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TANG TANG, Jenny</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms WONG WONG, Yoyo</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 15)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6"><i><b>
                                                            <font>Team 2</font>
                                                        </b></i></td>
                                            </tr>

                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Mabel</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Appointments) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LEE LEE, Esther</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Appointments) 2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms TING TING, Joanne</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 5</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr LAM LAM, Ivan</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 6</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LIN LIN, Michelle</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 7</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms YEUNG YEUNG, Windy</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Appointments) 8</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr KUNG Kam Yau</td>
                                <td bgcolor="#FFFFFF">Clerical Officer I (Appointments)2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Miss CHAN Lok Lam, Laura</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Ruby</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Dora</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LAM LAM, Phoebe</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LI LI, Frances</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms POON POON, Lily</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms YEUNG YEUNG, Winnie</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 14)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6">
                                    &nbsp;
                                </td>
                            </tr>

                            <tr>
                                <td colspan="6" align="center">

                                    <table border="0" width="100%">
                                        <tbody>
                                            <tr>
                                                <td colspan="6" align="center"><b>
                                                        <font size="+1">Digitalization and Administration Section</font>
                                                    </b></td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Fax&nbsp;&nbsp;</b></td>
                                                <td>0000-0000</td>
                                            </tr>
                                            <tr>
                                                <td align="right" valign="top" width="50%" colspan="5">
                                                    <b>Email&nbsp;&nbsp;</b></td>
                                                <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                                            </tr>
                                            <tr>
                                                <td colspan="6">&nbsp;</td>
                                            </tr> <!-- blank line -->
                                        </tbody>
                                    </table>

                                </td>
                            </tr>
                            <form></form>
                            <tr>
                                <th width="20%">Full Name</th>
                                <th width="20%">Post / Function</th>
                                <th width="20%">Telephone / Mobile / Pager</th>
                                <th width="10%">Fax</th>
                                <th width="10%">Email</th>
                                <th width="10%">Select All<br><input type="checkbox" name="dummy"
                                        onclick="selectCheckBox(this.form, this.checked)"></th>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Venus</td>
                                <td bgcolor="#FFFFFF">Human Resources Manager (Digitalization and Administration)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHOI CHOI, Doris</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Digitalization and
                                    Administration)1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr WONG Ryan Shek Ming</td>
                                <td bgcolor="#FFFFFF">Senior Human Resources Officer (Digitalization and
                                    Administration)2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LAU LAU, Stephanie</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Digitalization and Administration)1</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms LAU LAU, Natalie</td>
                                <td bgcolor="#FFFFFF">Human Resources Officer (Digitalization and Administration)2</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr TSOI TSOI, Leon</td>
                                <td bgcolor="#FFFFFF">Assistant Systems Manager</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Sandy</td>
                                <td bgcolor="#FFFFFF">Executive Assistant I (Digitalization and Administration)</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Mr CHAN CHAN, Jackson</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms CHAN CHAN, Catherine</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms NG NG, Doris</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail"
                                        value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF">Ms YU YU, Caney</td>
                                <td bgcolor="#FFFFFF">Registry</td>
                                <td bgcolor="#FFFFFF">0000-0000&nbsp;&nbsp;</td>
                                <td bgcolor="#FFFFFF">0000-0000</td>
                                <td bgcolor="#FFFFFF"><input type="hidden" name="mail" value="<EMAIL>"><a
                                        href="mailto:<EMAIL>"><EMAIL></a></td>
                                <td bgcolor="#FFFFFF" align="center"><input type="checkbox" name="writeMail"></td>
                            </tr>
                            <tr>
                                <td bgcolor="#FFFFFF" colspan="5">&nbsp;</td>
                                <td bgcolor="#FFFFFF" align="center"><input type="button" name="mailSend" value="Send"
                                        onclick="javascript:sendMail(this.form, 11)"></td>
                            </tr>

                            <tr>
                                <td colspan="6">&nbsp;</td>
                            </tr> <!-- blank line -->


                            <tr>
                                <td colspan="6" align="center">
                                    &nbsp;
                                </td>
                            </tr>

                            <tr>
                                <td colspan="6" class="note">
                                    <font size="-1">
                                        <u>Remark</u>
                                        <ol type="1">
                                            <li>For those who are dialing from places outside Hong Kong, please dial the
                                                area code '852' before the telephone number.</li>
                                        </ol>
                                    </font>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>


        </tbody>
    </table>

    <font size="-1">
        <ul>
            <li>For any enquires, please contact us at <a
                    href="mailto:<EMAIL>"><EMAIL></a>.</li>
        </ul>
    </font>


    <div id="automa-palette"></div>
</body>
<div id="immersive-translate-browser-popup" style="all: initial"></div>

</html>