# 🚀 快速安裝指南

## 5分鐘快速設置

### 步驟 1: 安裝 Tampermonkey (2分鐘)

根據您的瀏覽器選擇對應的安裝連結：

| 瀏覽器 | 安裝連結 | 說明 |
|--------|----------|------|
| **Chrome** | [點擊安裝](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo) | 最推薦 |
| **Firefox** | [點擊安裝](https://addons.mozilla.org/firefox/addon/tampermonkey/) | 完全支援 |
| **Edge** | [點擊安裝](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd) | 完全支援 |
| **Safari** | [點擊安裝](https://apps.apple.com/app/tampermonkey/id1482490089) | 需要 macOS 10.12+ |

### 步驟 2: 安裝腳本 (2分鐘)

1. **打開 Tampermonkey 管理面板**
   - 點擊瀏覽器工具欄中的 Tampermonkey 圖標 🐵
   - 選擇 "管理面板" 或 "Dashboard"

2. **創建新腳本**
   - 點擊 "+" 按鈕或 "Create a new script"
   - 刪除編輯器中的所有默認內容

3. **複製腳本代碼**
   - 打開 [`tampermonkey-contact-extractor.js`](tampermonkey-contact-extractor.js) 文件
   - 全選並複製所有內容 (Ctrl+A, Ctrl+C)

4. **粘貼並保存**
   - 在 Tampermonkey 編輯器中粘貼代碼 (Ctrl+V)
   - 按 Ctrl+S 保存，或點擊 "File" → "Save"

### 步驟 3: 測試功能 (1分鐘)

1. **打開聯絡人目錄頁面**
   - 打開您的 `raw.html` 文件
   - 或訪問相關的聯絡人目錄網站

2. **查看導出按鈕**
   - 頁面右上角應該出現綠色的 "📥 導出聯絡人為 CSV" 按鈕
   - 如果沒有看到，請檢查下方的故障排除

3. **執行導出**
   - 點擊導出按鈕
   - 等待處理完成
   - CSV 文件會自動下載

## ✅ 安裝成功標誌

安裝成功後，您應該看到：

- ✅ Tampermonkey 圖標在瀏覽器工具欄中顯示為啟用狀態
- ✅ 聯絡人目錄頁面右上角出現綠色導出按鈕
- ✅ 點擊按鈕後能成功下載 CSV 文件
- ✅ CSV 文件包含正確的聯絡人資料

## 🔧 快速故障排除

### 問題 1: 看不到 Tampermonkey 圖標
**解決方案:**
- 確認擴展已正確安裝
- 檢查瀏覽器是否允許擴展運行
- 重新啟動瀏覽器

### 問題 2: 看不到導出按鈕
**解決方案:**
- 確認腳本已保存並啟用
- 檢查頁面 URL 是否匹配腳本規則
- 按 F5 刷新頁面

### 問題 3: 點擊按鈕沒有反應
**解決方案:**
- 按 F12 打開開發者工具
- 查看 Console 標籤中的錯誤訊息
- 確保瀏覽器允許文件下載

### 問題 4: CSV 文件中文亂碼
**解決方案:**
- 使用 Google Sheets 或 LibreOffice Calc 打開
- 在 Excel 中選擇 UTF-8 編碼導入

## 🎯 支援的頁面類型

腳本會在以下頁面自動啟用：

- ✅ `file://*/raw.html` (本地 HTML 文件)
- ✅ `*://intra.abc.edu.hk/directory/*` (內部目錄網站)
- ✅ `*://*/directory/*` (任何包含 directory 的網站)
- ✅ 包含聯絡人表格的類似頁面

## 📱 移動設備支援

| 平台 | 支援狀況 | 說明 |
|------|----------|------|
| **Android Chrome** | ✅ 完全支援 | 需要安裝 Tampermonkey |
| **Android Firefox** | ✅ 完全支援 | 需要安裝 Tampermonkey |
| **iOS Safari** | ⚠️ 有限支援 | 需要 iOS 13+ 和 Tampermonkey App |
| **iOS Chrome** | ❌ 不支援 | iOS 限制 |

## 🔄 更新腳本

當有新版本時：

1. 在 Tampermonkey 管理面板中找到腳本
2. 點擊腳本名稱進入編輯模式
3. 替換為新版本代碼
4. 保存更改

## 📞 需要幫助？

如果遇到問題：

1. 📖 查看 [詳細安裝說明](安裝和使用說明.md)
2. 🧪 使用 [測試頁面](../test/test-extractor.html) 驗證功能
3. 🔍 檢查瀏覽器控制台的錯誤訊息
4. 💻 嘗試使用 [演示腳本](../test/demo-script.js) 進行調試

## 🎉 安裝完成！

恭喜！您已成功安裝聯絡人目錄 CSV 導出器。現在您可以：

- 🔍 自動提取聯絡人資料
- 📊 導出為標準 CSV 格式
- 📁 輕鬆管理和分析聯絡人資訊
- 🔄 隨時更新和維護資料

享受高效的聯絡人管理體驗！ 🚀
